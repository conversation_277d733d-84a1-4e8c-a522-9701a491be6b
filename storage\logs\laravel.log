[2025-08-27 19:19:27] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1133): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1043): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(994): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(198): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#47 {main}
"} 
[2025-08-27 19:19:31] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1133): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1043): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(994): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(258): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(216): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 {main}
"} 
[2025-08-27 19:20:31] local.ERROR: Route [admin.index] not defined. {"view":{"view":"C:\\xampp\\htdocs\\safedestssss\\resources\\views\\admin\\reports\\index.blade.php","data":{"jsTranslations":"<pre class=sf-dump id=sf-dump-882621635 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"24393 characters\">{&quot;Dashboards&quot;:&quot;Dashboards&quot;,&quot;Dashboard&quot;:&quot;Dashboard&quot;,&quot;eCommerce&quot;:&quot;eCommerce&quot;,&quot;CRM&quot;:&quot;CRM&quot;,&quot;Layouts&quot;:&quot;Layouts&quot;,&quot;Collapsed menu&quot;:&quot;Collapsed menu&quot;,&quot;Content navbar&quot;:&quot;Content navbar&quot;,&quot;Content nav + Sidebar&quot;:&quot;Content nav + Sidebar&quot;,&quot;Horizontal&quot;:&quot;Horizontal&quot;,&quot;Vertical&quot;:&quot;Vertical&quot;,&quot;Without menu&quot;:&quot;Without menu&quot;,&quot;Without navbar&quot;:&quot;Without navbar&quot;,&quot;Fluid&quot;:&quot;Fluid&quot;,&quot;Container&quot;:&quot;Container&quot;,&quot;Blank&quot;:&quot;Blank&quot;,&quot;Laravel Example&quot;:&quot;Laravel Example&quot;,&quot;User Management&quot;:&quot;User Management&quot;,&quot;Apps&quot;:&quot;Apps&quot;,&quot;Email&quot;:&quot;Email&quot;,&quot;Chat&quot;:&quot;Chat&quot;,&quot;Calendar&quot;:&quot;Calendar&quot;,&quot;Kanban&quot;:&quot;Kanban&quot;,&quot;Products&quot;:&quot;Products&quot;,&quot;Add Product&quot;:&quot;Add Product&quot;,&quot;Product List&quot;:&quot;Product List&quot;,&quot;Category List&quot;:&quot;Category List&quot;,&quot;Category&quot;:&quot;Category&quot;,&quot;Order&quot;:&quot;Order&quot;,&quot;Order List&quot;:&quot;Order List&quot;,&quot;Order Details&quot;:&quot;Order Details&quot;,&quot;Customer&quot;:&quot;Customer&quot;,&quot;All Customer&quot;:&quot;All Customer&quot;,&quot;All Customers&quot;:&quot;All Customers&quot;,&quot;Customer Details&quot;:&quot;Customer Details&quot;,&quot;Overview&quot;:&quot;Overview&quot;,&quot;Address &amp; Billing&quot;:&quot;Address &amp; Billing&quot;,&quot;Manage Reviews&quot;:&quot;Manage Reviews&quot;,&quot;Referrals&quot;:&quot;Referrals&quot;,&quot;Settings&quot;:&quot;Settings&quot;,&quot;Store Details&quot;:&quot;Store Details&quot;,&quot;Payments&quot;:&quot;Payments&quot;,&quot;Shipping &amp; Delivery&quot;:&quot;Shipping &amp; Delivery&quot;,&quot;Locations&quot;:&quot;Locations&quot;,&quot;Roles &amp; Permissions&quot;:&quot;Roles &amp; Permissions&quot;,&quot;Add new roles with customized permissions as per your requirement&quot;:&quot;Add new roles with customized permissions as per your requirement&quot;,&quot;Add New Role&quot;:&quot;Add New Role&quot;,&quot;Edit Role&quot;:&quot;Edit Role&quot;,&quot;Role&quot;:&quot;Role&quot;,&quot;Created At&quot;:&quot;Created At&quot;,&quot;Actions&quot;:&quot;Actions&quot;,&quot;Search User&quot;:&quot;Search User&quot;,&quot;Displaying _START_ to _END_ of _TOTAL_ entries&quot;:&quot;Displaying _START_ to _END_ of _TOTAL_ entries&quot;,&quot;Showing _START_ to _END_ of _TOTAL_ entries&quot;:&quot;Showing _START_ to _END_ of _TOTAL_ entries&quot;,&quot;Search...&quot;:&quot;Search...&quot;,&quot;add new role&quot;:&quot;add new role&quot;,&quot;role&quot;:&quot;role&quot;,&quot;role name&quot;:&quot;role name&quot;,&quot;guard&quot;:&quot;guard&quot;,&quot;Administrator&quot;:&quot;Administrator&quot;,&quot;Driver&quot;:&quot;Driver&quot;,&quot;Details of&quot;:&quot;Details of&quot;,&quot;There is no Permissions found!&quot;:&quot;There is no Permissions found!&quot;,&quot;Error!! can not fiche any Permission&quot;:&quot;Error!! can not fiche any Permission&quot;,&quot;permissions&quot;:&quot;permissions&quot;,&quot;Roles&quot;:&quot;Roles&quot;,&quot;Role Name&quot;:&quot;Role Name&quot;,&quot;Guard&quot;:&quot;Guard&quot;,&quot;Permissions&quot;:&quot;Permissions&quot;,&quot;Home&quot;:&quot;Home&quot;,&quot;Profile&quot;:&quot;Profile&quot;,&quot;Messages&quot;:&quot;Messages&quot;,&quot;Close&quot;:&quot;Close&quot;,&quot;Submit&quot;:&quot;Submit&quot;,&quot;Users&quot;:&quot;Users&quot;,&quot;Active Users&quot;:&quot;Active Users&quot;,&quot;Inactive Users&quot;:&quot;Inactive Users&quot;,&quot;Pending Users&quot;:&quot;Pending Users&quot;,&quot;Add New User&quot;:&quot;Add New User&quot;,&quot;User&quot;:&quot;User&quot;,&quot;Phone&quot;:&quot;Phone&quot;,&quot;Status&quot;:&quot;Status&quot;,&quot;Reset Password&quot;:&quot;Reset Password&quot;,&quot;Add new User&quot;:&quot;Add new User&quot;,&quot;Edit User&quot;:&quot;Edit User&quot;,&quot;Main&quot;:&quot;Main&quot;,&quot;Additional&quot;:&quot;Additional&quot;,&quot;Full Name&quot;:&quot;Full Name&quot;,&quot;<EMAIL>&quot;:&quot;<EMAIL>&quot;,&quot;Enter phone number&quot;:&quot;Enter phone number&quot;,&quot;Password&quot;:&quot;Password&quot;,&quot;Confirm Password&quot;:&quot;Confirm Password&quot;,&quot;User Role&quot;:&quot;User Role&quot;,&quot;Teams&quot;:&quot;Teams&quot;,&quot;Customers&quot;:&quot;Customers&quot;,&quot;Active Customers&quot;:&quot;Active Customers&quot;,&quot;Unverified Customers&quot;:&quot;Unverified Customers&quot;,&quot;Blocked Customers&quot;:&quot;Blocked Customers&quot;,&quot;name&quot;:&quot;name&quot;,&quot;email&quot;:&quot;email&quot;,&quot;phone&quot;:&quot;phone&quot;,&quot;tags&quot;:&quot;tags&quot;,&quot;status&quot;:&quot;status&quot;,&quot;created at&quot;:&quot;created at&quot;,&quot;actions&quot;:&quot;actions&quot;,&quot;Add New Customer&quot;:&quot;Add New Customer&quot;,&quot;Customer Role&quot;:&quot;Customer Role&quot;,&quot;Select Role&quot;:&quot;Select Role&quot;,&quot;Company Info&quot;:&quot;Company Info&quot;,&quot;Company Name&quot;:&quot;Company Name&quot;,&quot;enter company name&quot;:&quot;enter company name&quot;,&quot;Company Address&quot;:&quot;Company Address&quot;,&quot;enter company address&quot;:&quot;enter company address&quot;,&quot;Tags&quot;:&quot;Tags&quot;,&quot;Select Template&quot;:&quot;Select Template&quot;,&quot;-- Select Template&quot;:&quot;-- Select Template&quot;,&quot;--- Select Template&quot;:&quot;--- Select Template&quot;,&quot;Logistics&quot;:&quot;Logistics&quot;,&quot;Fleet&quot;:&quot;Fleet&quot;,&quot;Invoice&quot;:&quot;Invoice&quot;,&quot;Preview&quot;:&quot;Preview&quot;,&quot;Add&quot;:&quot;Add&quot;,&quot;Pages&quot;:&quot;Pages&quot;,&quot;User Profile&quot;:&quot;User Profile&quot;,&quot;Projects&quot;:&quot;Projects&quot;,&quot;Account Settings&quot;:&quot;Account Settings&quot;,&quot;Account&quot;:&quot;Account&quot;,&quot;Security&quot;:&quot;Security&quot;,&quot;Billing &amp; Plans&quot;:&quot;Billing &amp; Plans&quot;,&quot;Notifications&quot;:&quot;Notifications&quot;,&quot;Connections&quot;:&quot;Connections&quot;,&quot;FAQ&quot;:&quot;FAQ&quot;,&quot;Front Pages&quot;:&quot;Front Pages&quot;,&quot;Payment&quot;:&quot;Payment&quot;,&quot;Help Center&quot;:&quot;Help Center&quot;,&quot;Landing&quot;:&quot;Landing&quot;,&quot;Categories&quot;:&quot;Categories&quot;,&quot;Article&quot;:&quot;Article&quot;,&quot;Pricing&quot;:&quot;Pricing&quot;,&quot;Error&quot;:&quot;Error&quot;,&quot;Coming Soon&quot;:&quot;Coming Soon&quot;,&quot;Under Maintenance&quot;:&quot;Under Maintenance&quot;,&quot;Not Authorized&quot;:&quot;Not Authorized&quot;,&quot;Authentications&quot;:&quot;Authentications&quot;,&quot;Login&quot;:&quot;Login&quot;,&quot;Register&quot;:&quot;Register&quot;,&quot;Verify Email&quot;:&quot;Verify Email&quot;,&quot;Forgot Password&quot;:&quot;Forgot Password&quot;,&quot;Two Steps&quot;:&quot;Two Steps&quot;,&quot;Basic&quot;:&quot;Basic&quot;,&quot;Cover&quot;:&quot;Cover&quot;,&quot;Multi-steps&quot;:&quot;Multi-steps&quot;,&quot;Modal Examples&quot;:&quot;Modal Examples&quot;,&quot;Wizard Examples&quot;:&quot;Wizard Examples&quot;,&quot;Checkout&quot;:&quot;Checkout&quot;,&quot;Property Listing&quot;:&quot;Property Listing&quot;,&quot;Create Deal&quot;:&quot;Create Deal&quot;,&quot;Icons&quot;:&quot;Icons&quot;,&quot;Tabler&quot;:&quot;Tabler&quot;,&quot;Fontawesome&quot;:&quot;Fontawesome&quot;,&quot;User interface&quot;:&quot;User interface&quot;,&quot;Accordion&quot;:&quot;Accordion&quot;,&quot;Alerts&quot;:&quot;Alerts&quot;,&quot;App Brand&quot;:&quot;App Brand&quot;,&quot;Badges&quot;:&quot;Badges&quot;,&quot;Buttons&quot;:&quot;Buttons&quot;,&quot;Cards&quot;:&quot;Cards&quot;,&quot;Advance&quot;:&quot;Advance&quot;,&quot;Statistics&quot;:&quot;Statistics&quot;,&quot;Analytics&quot;:&quot;Analytics&quot;,&quot;Carousel&quot;:&quot;Carousel&quot;,&quot;Collapse&quot;:&quot;Collapse&quot;,&quot;Dropdowns&quot;:&quot;Dropdowns&quot;,&quot;Footer&quot;:&quot;Footer&quot;,&quot;List Groups&quot;:&quot;List Groups&quot;,&quot;Modals&quot;:&quot;Modals&quot;,&quot;Menu&quot;:&quot;Menu&quot;,&quot;Navbar&quot;:&quot;Navbar&quot;,&quot;Offcanvas&quot;:&quot;Offcanvas&quot;,&quot;Pagination &amp; Breadcrumbs&quot;:&quot;Pagination &amp; Breadcrumbs&quot;,&quot;Progress&quot;:&quot;Progress&quot;,&quot;Spinners&quot;:&quot;Spinners&quot;,&quot;Tabs &amp; Pills&quot;:&quot;Tabs &amp; Pills&quot;,&quot;Toasts&quot;:&quot;Toasts&quot;,&quot;Tooltips &amp; Popovers&quot;:&quot;Tooltips &amp; Popovers&quot;,&quot;Typography&quot;:&quot;Typography&quot;,&quot;Extended UI&quot;:&quot;Extended UI&quot;,&quot;Avatar&quot;:&quot;Avatar&quot;,&quot;BlockUI&quot;:&quot;BlockUI&quot;,&quot;Drag &amp; Drop&quot;:&quot;Drag &amp; Drop&quot;,&quot;Media Player&quot;:&quot;Media Player&quot;,&quot;Perfect Scrollbar&quot;:&quot;Perfect Scrollbar&quot;,&quot;Star Ratings&quot;:&quot;Star Ratings&quot;,&quot;SweetAlert2&quot;:&quot;SweetAlert2&quot;,&quot;Text Divider&quot;:&quot;Text Divider&quot;,&quot;Timeline&quot;:&quot;Timeline&quot;,&quot;Fullscreen&quot;:&quot;Fullscreen&quot;,&quot;Tour&quot;:&quot;Tour&quot;,&quot;Treeview&quot;:&quot;Treeview&quot;,&quot;Miscellaneous&quot;:&quot;Miscellaneous&quot;,&quot;Misc&quot;:&quot;Misc&quot;,&quot;Form Elements&quot;:&quot;Form Elements&quot;,&quot;Basic Inputs&quot;:&quot;Basic Inputs&quot;,&quot;Input groups&quot;:&quot;Input groups&quot;,&quot;Custom Options&quot;:&quot;Custom Options&quot;,&quot;Editors&quot;:&quot;Editors&quot;,&quot;File Upload&quot;:&quot;File Upload&quot;,&quot;Pickers&quot;:&quot;Pickers&quot;,&quot;Select &amp; Tags&quot;:&quot;Select &amp; Tags&quot;,&quot;Sliders&quot;:&quot;Sliders&quot;,&quot;Switches&quot;:&quot;Switches&quot;,&quot;Extras&quot;:&quot;Extras&quot;,&quot;Form Layouts&quot;:&quot;Form Layouts&quot;,&quot;Vertical Form&quot;:&quot;Vertical Form&quot;,&quot;Horizontal Form&quot;:&quot;Horizontal Form&quot;,&quot;Sticky Actions&quot;:&quot;Sticky Actions&quot;,&quot;Form Wizard&quot;:&quot;Form Wizard&quot;,&quot;Numbered&quot;:&quot;Numbered&quot;,&quot;Advanced&quot;:&quot;Advanced&quot;,&quot;Forms&quot;:&quot;Forms&quot;,&quot;Form Validation&quot;:&quot;Form Validation&quot;,&quot;Tables&quot;:&quot;Tables&quot;,&quot;Datatables&quot;:&quot;Datatables&quot;,&quot;Extensions&quot;:&quot;Extensions&quot;,&quot;Charts&quot;:&quot;Charts&quot;,&quot;Apex Charts&quot;:&quot;Apex Charts&quot;,&quot;ChartJS&quot;:&quot;ChartJS&quot;,&quot;Leaflet Maps&quot;:&quot;Leaflet Maps&quot;,&quot;Support&quot;:&quot;Support&quot;,&quot;Documentation&quot;:&quot;Documentation&quot;,&quot;Academy&quot;:&quot;Academy&quot;,&quot;My Course&quot;:&quot;My Course&quot;,&quot;Course Details&quot;:&quot;Course Details&quot;,&quot;Apps &amp; Pages&quot;:&quot;Apps &amp; Pages&quot;,&quot;Components&quot;:&quot;Components&quot;,&quot;Forms &amp; Tables&quot;:&quot;Forms &amp; Tables&quot;,&quot;Charts &amp; Maps&quot;:&quot;Charts &amp; Maps&quot;,&quot;Id&quot;:&quot;Id&quot;,&quot;General&quot;:&quot;General&quot;,&quot;Template&quot;:&quot;Template&quot;,&quot;Donut drag\\u00e9e jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.&quot;:&quot;Donut drag\\u00e9e jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.&quot;,&quot;Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.&quot;:&quot;Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.&quot;,&quot;Geo-fence&quot;:&quot;Geo-fence&quot;,&quot;It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.&quot;:&quot;It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.&quot;,&quot;Add New Geo-fence&quot;:&quot;Add New Geo-fence&quot;,&quot;\\ud83d\\udd0d Search Team&quot;:&quot;\\ud83d\\udd0d Search Team&quot;,&quot;Geofences&quot;:&quot;Geofences&quot;,&quot;Add Geo-fence&quot;:&quot;Add Geo-fence&quot;,&quot;Select Teams&quot;:&quot;Select Teams&quot;,&quot;Name&quot;:&quot;Name&quot;,&quot;Enter name&quot;:&quot;Enter name&quot;,&quot;Description&quot;:&quot;Description&quot;,&quot;Enter description&quot;:&quot;Enter description&quot;,&quot;The role name is required.&quot;:&quot;The role name is required.&quot;,&quot;The role name has already been taken.&quot;:&quot;The role name has already been taken.&quot;,&quot;The guard field is required.&quot;:&quot;The guard field is required.&quot;,&quot;The selected guard is invalid.&quot;:&quot;The selected guard is invalid.&quot;,&quot;At least one permission must be selected.&quot;:&quot;At least one permission must be selected.&quot;,&quot;Permissions must be an array.&quot;:&quot;Permissions must be an array.&quot;,&quot;Error to Save Role&quot;:&quot;Error to Save Role&quot;,&quot;Role Saved&quot;:&quot;Role Saved&quot;,&quot;This role can not be deleted&quot;:&quot;This role can not be deleted&quot;,&quot;There are users connected with this role&quot;:&quot;There are users connected with this role&quot;,&quot;Error to delete role&quot;:&quot;Error to delete role&quot;,&quot;Role deleted&quot;:&quot;Role deleted&quot;,&quot;Rate&quot;:&quot;Rate&quot;,&quot;Fixed&quot;:&quot;Fixed&quot;,&quot;Commission Rate&quot;:&quot;Commission Rate&quot;,&quot;Commission fixed Amount&quot;:&quot;Commission fixed Amount&quot;,&quot;General Settings&quot;:&quot;General Settings&quot;,&quot;You can manage the main and vital settings of the platform from here, so be careful.&quot;:&quot;You can manage the main and vital settings of the platform from here, so be careful.&quot;,&quot;Templates&quot;:&quot;Templates&quot;,&quot;Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more&quot;:&quot;Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more&quot;,&quot;Add New Template&quot;:&quot;Add New Template&quot;,&quot;Template Name&quot;:&quot;Template Name&quot;,&quot;enter the Template name&quot;:&quot;enter the Template name&quot;,&quot;Default Customer Template&quot;:&quot;Default Customer Template&quot;,&quot;Default Driver Template&quot;:&quot;Default Driver Template&quot;,&quot;Default User Template&quot;:&quot;Default User Template&quot;,&quot;Default Task Template&quot;:&quot;Default Task Template&quot;,&quot;Drivers Commission&quot;:&quot;Drivers Commission&quot;,&quot;Commission Type&quot;:&quot;Commission Type&quot;,&quot;The user id is required.&quot;:&quot;The user id is required.&quot;,&quot;The selected user does not exist.&quot;:&quot;The selected user does not exist.&quot;,&quot;The status field is required.&quot;:&quot;The status field is required.&quot;,&quot;Error to Change user Status&quot;:&quot;Error to Change user Status&quot;,&quot;User Status changed&quot;:&quot;User Status changed&quot;,&quot;The name field is required.&quot;:&quot;The name field is required.&quot;,&quot;The email field is required.&quot;:&quot;The email field is required.&quot;,&quot;The email has already been taken.&quot;:&quot;The email has already been taken.&quot;,&quot;The phone field is required.&quot;:&quot;The phone field is required.&quot;,&quot;The phone has already been taken.&quot;:&quot;The phone has already been taken.&quot;,&quot;The password field is required.&quot;:&quot;The password field is required.&quot;,&quot;The password and confirmation must match.&quot;:&quot;The password and confirmation must match.&quot;,&quot;The user role is required.&quot;:&quot;The user role is required.&quot;,&quot;The selected role is invalid.&quot;:&quot;The selected role is invalid.&quot;,&quot;Teams must be an array.&quot;:&quot;Teams must be an array.&quot;,&quot;Customers must be an array.&quot;:&quot;Customers must be an array.&quot;,&quot;The selected template is invalid.&quot;:&quot;The selected template is invalid.&quot;,&quot;The :label field is required.&quot;:&quot;The :label field is required.&quot;,&quot;Can not find the selected user&quot;:&quot;Can not find the selected user&quot;,&quot;User saved successfully&quot;:&quot;User saved successfully&quot;,&quot;User not found&quot;:&quot;User not found&quot;,&quot;Error to change reset password  status&quot;:&quot;Error to change reset password  status&quot;,&quot;User deleted&quot;:&quot;User deleted&quot;,&quot;The selected User has teams to mange. you can not delete hem right now&quot;:&quot;The selected User has teams to mange. you can not delete hem right now&quot;,&quot;Error to delete User&quot;:&quot;Error to delete User&quot;,&quot;Vehicles&quot;:&quot;Vehicles&quot;,&quot;Managing the types of vehicles and trucks that will provide delivery services on the platform&quot;:&quot;Managing the types of vehicles and trucks that will provide delivery services on the platform&quot;,&quot;Vehicles Types&quot;:&quot;Vehicles Types&quot;,&quot;Vehicles Sizes&quot;:&quot;Vehicles Sizes&quot;,&quot;vehicle name&quot;:&quot;vehicle name&quot;,&quot;English name&quot;:&quot;English name&quot;,&quot;save&quot;:&quot;save&quot;,&quot;types&quot;:&quot;types&quot;,&quot;Select vehicle&quot;:&quot;Select vehicle&quot;,&quot;select vehicle&quot;:&quot;select vehicle&quot;,&quot;Flitter by vehicle&quot;:&quot;Flitter by vehicle&quot;,&quot;all vehicle&quot;:&quot;all vehicle&quot;,&quot;vehicle&quot;:&quot;vehicle&quot;,&quot;type name&quot;:&quot;type name&quot;,&quot;sizes&quot;:&quot;sizes&quot;,&quot;size&quot;:&quot;size&quot;,&quot;Select vehicle Type&quot;:&quot;Select vehicle Type&quot;,&quot;Flitter by vehicle type&quot;:&quot;Flitter by vehicle type&quot;,&quot;select vehicle type&quot;:&quot;select vehicle type&quot;,&quot;vehicle type&quot;:&quot;vehicle type&quot;,&quot;No data available&quot;:&quot;No data available&quot;,&quot;select vehicle Size&quot;:&quot;select vehicle Size&quot;,&quot;Add New Tag&quot;:&quot;Add New Tag&quot;,&quot;tag name&quot;:&quot;tag name&quot;,&quot;enter the tag name&quot;:&quot;enter the tag name&quot;,&quot;tag slug&quot;:&quot;tag slug&quot;,&quot;enter the tag slug&quot;:&quot;enter the tag slug&quot;,&quot;Select Tags&quot;:&quot;Select Tags&quot;,&quot;The tag name is required.&quot;:&quot;The tag name is required.&quot;,&quot;The tag name has already been taken.&quot;:&quot;The tag name has already been taken.&quot;,&quot;The tag slug is required.&quot;:&quot;The tag slug is required.&quot;,&quot;The tag slug has already been taken.&quot;:&quot;The tag slug has already been taken.&quot;,&quot;The description must be a string.&quot;:&quot;The description must be a string.&quot;,&quot;The description may not be greater than 400 characters.&quot;:&quot;The description may not be greater than 400 characters.&quot;,&quot;Can not find the selected Tag&quot;:&quot;Can not find the selected Tag&quot;,&quot;Error: can not save the Tag&quot;:&quot;Error: can not save the Tag&quot;,&quot;Tag saved successfully&quot;:&quot;Tag saved successfully&quot;,&quot;Error to find selected Tag&quot;:&quot;Error to find selected Tag&quot;,&quot;Error to delete Tag&quot;:&quot;Error to delete Tag&quot;,&quot;Tag deleted&quot;:&quot;Tag deleted&quot;,&quot;The geofence name is required.&quot;:&quot;The geofence name is required.&quot;,&quot;The geofence name has already been taken.&quot;:&quot;The geofence name has already been taken.&quot;,&quot;The coordinates field is required.&quot;:&quot;The coordinates field is required.&quot;,&quot;The coordinates must be a string.&quot;:&quot;The coordinates must be a string.&quot;,&quot;Can not find the selected Geo-Fence&quot;:&quot;Can not find the selected Geo-Fence&quot;,&quot;error to save Geo-Fence&quot;:&quot;error to save Geo-Fence&quot;,&quot;Geo-Fence saved successfully&quot;:&quot;Geo-Fence saved successfully&quot;,&quot;Error to delete Geo-fence&quot;:&quot;Error to delete Geo-fence&quot;,&quot;Geo-fence deleted&quot;:&quot;Geo-fence deleted&quot;,&quot;Points&quot;:&quot;Points&quot;,&quot;Add New Point&quot;:&quot;Add New Point&quot;,&quot;address&quot;:&quot;address&quot;,&quot;customer&quot;:&quot;customer&quot;,&quot;enter the point name&quot;:&quot;enter the point name&quot;,&quot;enter the point address&quot;:&quot;enter the point address&quot;,&quot;Location&quot;:&quot;Location&quot;,&quot;confirm location&quot;:&quot;confirm location&quot;,&quot;Contact name&quot;:&quot;Contact name&quot;,&quot;enter the point contact name&quot;:&quot;enter the point contact name&quot;,&quot;Contact phone&quot;:&quot;Contact phone&quot;,&quot;enter the point contact phone&quot;:&quot;enter the point contact phone&quot;,&quot;Select Customer&quot;:&quot;Select Customer&quot;,&quot;The point name is required.&quot;:&quot;The point name is required.&quot;,&quot;The point name must be a string.&quot;:&quot;The point name must be a string.&quot;,&quot;The contact name must be a string.&quot;:&quot;The contact name must be a string.&quot;,&quot;The contact name may not be greater than 400 characters.&quot;:&quot;The contact name may not be greater than 400 characters.&quot;,&quot;The contact phone must be a string.&quot;:&quot;The contact phone must be a string.&quot;,&quot;The contact phone may not be greater than 50 characters.&quot;:&quot;The contact phone may not be greater than 50 characters.&quot;,&quot;The address field is required.&quot;:&quot;The address field is required.&quot;,&quot;The address must be a string.&quot;:&quot;The address must be a string.&quot;,&quot;The address may not be greater than 500 characters.&quot;:&quot;The address may not be greater than 500 characters.&quot;,&quot;The latitude field is required.&quot;:&quot;The latitude field is required.&quot;,&quot;The latitude must be a number.&quot;:&quot;The latitude must be a number.&quot;,&quot;The longitude field is required.&quot;:&quot;The longitude field is required.&quot;,&quot;The longitude must be a number.&quot;:&quot;The longitude must be a number.&quot;,&quot;The selected customer is invalid.&quot;:&quot;The selected customer is invalid.&quot;,&quot;Can not find the selected Point&quot;:&quot;Can not find the selected Point&quot;,&quot;Error: can not save the Point&quot;:&quot;Error: can not save the Point&quot;,&quot;Point saved successfully&quot;:&quot;Point saved successfully&quot;,&quot;Error to delete Point. its connect with pricing mater&quot;:&quot;Error to delete Point. its connect with pricing mater&quot;,&quot;Error to delete Point&quot;:&quot;Error to delete Point&quot;,&quot;Point deleted&quot;:&quot;Point deleted&quot;,&quot;Blockages&quot;:&quot;Blockages&quot;,&quot;Add a new Blockage&quot;:&quot;Add a new Blockage&quot;,&quot;type&quot;:&quot;type&quot;,&quot;description&quot;:&quot;description&quot;,&quot;coordinates&quot;:&quot;coordinates&quot;,&quot;Block Type&quot;:&quot;Block Type&quot;,&quot;Select Type&quot;:&quot;Select Type&quot;,&quot;Point Closed&quot;:&quot;Point Closed&quot;,&quot;Line Closed&quot;:&quot;Line Closed&quot;,&quot;Block Description&quot;:&quot;Block Description&quot;,&quot;optional&quot;:&quot;optional&quot;,&quot;draw the Points on the map&quot;:&quot;draw the Points on the map&quot;,&quot;Save&quot;:&quot;Save&quot;,&quot;Cancel&quot;:&quot;Cancel&quot;,&quot;The blockage type is required.&quot;:&quot;The blockage type is required.&quot;,&quot;The selected blockage type is invalid.&quot;:&quot;The selected blockage type is invalid.&quot;,&quot;Error: can not save the Blockage&quot;:&quot;Error: can not save the Blockage&quot;,&quot;Blockage saved successfully&quot;:&quot;Blockage saved successfully&quot;,&quot;Error to delete Blockage&quot;:&quot;Error to delete Blockage&quot;,&quot;Blockage deleted&quot;:&quot;Blockage deleted&quot;,&quot;Pricing Methods&quot;:&quot;Pricing Methods&quot;,&quot;Method&quot;:&quot;Method&quot;,&quot;Add New Method&quot;:&quot;Add New Method&quot;,&quot;Method Name&quot;:&quot;Method Name&quot;,&quot;enter the Method name&quot;:&quot;enter the Method name&quot;,&quot;Edit Method&quot;:&quot;Edit Method&quot;,&quot;The method name is required.&quot;:&quot;The method name is required.&quot;,&quot;The method name has already been taken.&quot;:&quot;The method name has already been taken.&quot;,&quot;The description field is required.&quot;:&quot;The description field is required.&quot;,&quot;Can not find the selected Pricing Method&quot;:&quot;Can not find the selected Pricing Method&quot;,&quot;error to save Pricing Method&quot;:&quot;error to save Pricing Method&quot;,&quot;Pricing Method saved successfully&quot;:&quot;Pricing Method saved successfully&quot;,&quot;Pricing Method not found&quot;:&quot;Pricing Method not found&quot;,&quot;Error to change Pricing Method status&quot;:&quot;Error to change Pricing Method status&quot;,&quot;label&quot;:&quot;label&quot;,&quot;driver can&quot;:&quot;driver can&quot;,&quot;customer can&quot;:&quot;customer can&quot;,&quot;value&quot;:&quot;value&quot;,&quot;require&quot;:&quot;require&quot;,&quot;Select Values&quot;:&quot;Select Values&quot;,&quot;Value&quot;:&quot;Value&quot;,&quot;Display Name&quot;:&quot;Display Name&quot;,&quot;Action&quot;:&quot;Action&quot;,&quot;More&quot;:&quot;More&quot;,&quot;Customers Selections&quot;:&quot;Customers Selections&quot;,&quot;Apply to All Customers&quot;:&quot;Apply to All Customers&quot;,&quot;Use customers tags&quot;:&quot;Use customers tags&quot;,&quot;Use Specific customers&quot;:&quot;Use Specific customers&quot;,&quot;Vehicles Selections&quot;:&quot;Vehicles Selections&quot;,&quot;Base Fare&quot;:&quot;Base Fare&quot;,&quot;Base Distance&quot;:&quot;Base Distance&quot;,&quot;Base Waiting&quot;:&quot;Base Waiting&quot;,&quot;Distance Fare&quot;:&quot;Distance Fare&quot;,&quot;Waiting Fare&quot;:&quot;Waiting Fare&quot;,&quot;Dynamic Pricing Based on Field Values&quot;:&quot;Dynamic Pricing Based on Field Values&quot;,&quot;add field&quot;:&quot;add field&quot;,&quot;Dynamic Pricing Based on Geo-fence&quot;:&quot;Dynamic Pricing Based on Geo-fence&quot;,&quot;add geofence&quot;:&quot;add geofence&quot;,&quot;Commission&quot;:&quot;Commission&quot;,&quot;VAT Commission&quot;:&quot;VAT Commission&quot;,&quot;Service Tax Commission&quot;:&quot;Service Tax Commission&quot;,&quot;Discount Fare&quot;:&quot;Discount Fare&quot;,&quot;Discount percentage %&quot;:&quot;Discount percentage %&quot;,&quot;The rule name is required.&quot;:&quot;The rule name is required.&quot;,&quot;The rule name must be a string.&quot;:&quot;The rule name must be a string.&quot;,&quot;The rule name may not be greater than 255 characters.&quot;:&quot;The rule name may not be greater than 255 characters.&quot;,&quot;The rule name has already been taken.&quot;:&quot;The rule name has already been taken.&quot;,&quot;The decimal places field is required.&quot;:&quot;The decimal places field is required.&quot;,&quot;The decimal places must be an integer.&quot;:&quot;The decimal places must be an integer.&quot;,&quot;The decimal places must be at least 0.&quot;:&quot;The decimal places must be at least 0.&quot;,&quot;The decimal places may not be greater than 10.&quot;:&quot;The decimal places may not be greater than 10.&quot;,&quot;The form template is required.&quot;:&quot;The form template is required.&quot;,&quot;The form template id must be an integer.&quot;:&quot;The form template id must be an integer.&quot;,&quot;The selected form template is invalid.&quot;:&quot;The selected form template is invalid.&quot;,&quot;At least one customer must be selected.&quot;:&quot;At least one customer must be selected.&quot;,&quot;Each customer is required.&quot;:&quot;Each customer is required.&quot;,&quot;Each customer id must be an integer.&quot;:&quot;Each customer id must be an integer.&quot;,&quot;At least one tag must be selected.&quot;:&quot;At least one tag must be selected.&quot;,&quot;Tags must be an array.&quot;:&quot;Tags must be an array.&quot;,&quot;Each tag is required.&quot;:&quot;Each tag is required.&quot;,&quot;Each tag id must be an integer.&quot;:&quot;Each tag id must be an integer.&quot;,&quot;The selected tag is invalid.&quot;:&quot;The selected tag is invalid.&quot;,&quot;At least one vehicle size must be selected.&quot;:&quot;At least one vehicle size must be selected.&quot;,&quot;Sizes must be an array.&quot;:&quot;Sizes must be an array.&quot;,&quot;Each size id must be an integer.&quot;:&quot;Each size id must be an integer.&quot;,&quot;The selected vehicle size is invalid.&quot;:&quot;The selected vehicle size is invalid.&quot;,&quot;The base fare field is required.&quot;:&quot;The base fare field is required.&quot;,&quot;The base fare must be a number.&quot;:&quot;The base fare must be a number.&quot;,&quot;The base fare must be at least 0.&quot;:&quot;The base fare must be at least 0.&quot;,&quot;The base distance field is required.&quot;:&quot;The base distance field is required.&quot;,&quot;The base distance must be a number.&quot;:&quot;The base distance must be a number.&quot;,&quot;The base distance must be at least 0.&quot;:&quot;The base distance must be at least 0.&quot;,&quot;The base waiting field is required.&quot;:&quot;The base waiting field is required.&quot;,&quot;The base waiting must be a number.&quot;:&quot;The base waiting must be a number.&quot;,&quot;The base waiting must be at least 0.&quot;:&quot;The base waiting must be at least 0.&quot;,&quot;The distance fare field is required.&quot;:&quot;The distance fare field is required.&quot;,&quot;The distance fare must be a number.&quot;:&quot;The distance fare must be a number.&quot;,&quot;The distance fare must be at least 0.&quot;:&quot;The distance fare must be at least 0.&quot;,&quot;The waiting fare field is required.&quot;:&quot;The waiting fare field is required.&quot;,&quot;The waiting fare must be a number.&quot;:&quot;The waiting fare must be a number.&quot;,&quot;The waiting fare must be at least 0.&quot;:&quot;The waiting fare must be at least 0.&quot;,&quot;The VAT commission field is required.&quot;:&quot;The VAT commission field is required.&quot;,&quot;The VAT commission must be a number.&quot;:&quot;The VAT commission must be a number.&quot;,&quot;The VAT commission must be at least 0.&quot;:&quot;The VAT commission must be at least 0.&quot;,&quot;The VAT commission may not be greater than 100.&quot;:&quot;The VAT commission may not be greater than 100.&quot;,&quot;The service commission field is required.&quot;:&quot;The service commission field is required.&quot;,&quot;The service commission must be a number.&quot;:&quot;The service commission must be a number.&quot;,&quot;The service commission must be at least 0.&quot;:&quot;The service commission must be at least 0.&quot;,&quot;The service commission may not be greater than 100.&quot;:&quot;The service commission may not be greater than 100.&quot;,&quot;The discount must be a number.&quot;:&quot;The discount must be a number.&quot;,&quot;The discount must be at least 0.&quot;:&quot;The discount must be at least 0.&quot;,&quot;The discount may not be greater than 100.&quot;:&quot;The discount may not be greater than 100.&quot;,&quot;Task Done&quot;:&quot;Task Done&quot;,&quot;Running Tasks&quot;:&quot;Running Tasks&quot;,&quot;Details&quot;:&quot;Details&quot;,&quot;Edit&quot;:&quot;Edit&quot;,&quot;Suspend&quot;:&quot;Suspend&quot;,&quot;Tasks&quot;:&quot;Tasks&quot;,&quot;Total&quot;:&quot;Total&quot;,&quot;Issued Date&quot;:&quot;Issued Date&quot;,&quot;Additional Fields&quot;:&quot;Additional Fields&quot;,&quot;No additional data found for this customer.&quot;:&quot;No additional data found for this customer.&quot;,&quot;View&quot;:&quot;View&quot;,&quot;Change Status&quot;:&quot;Change Status&quot;,&quot;Create Wallet&quot;:&quot;Create Wallet&quot;,&quot;The customer id is required.&quot;:&quot;The customer id is required.&quot;,&quot;The selected customer does not exist.&quot;:&quot;The selected customer does not exist.&quot;,&quot;You do not have permission to do actions to this record&quot;:&quot;You do not have permission to do actions to this record&quot;,&quot;Error to Change Customer Status&quot;:&quot;Error to Change Customer Status&quot;,&quot;Customer Status changed&quot;:&quot;Customer Status changed&quot;,&quot;Can not find the selected Customer&quot;:&quot;Can not find the selected Customer&quot;,&quot;Error: can not save the Customer&quot;:&quot;Error: can not save the Customer&quot;,&quot;Customer saved successfully&quot;:&quot;Customer saved successfully&quot;,&quot;Error to delete Customer&quot;:&quot;Error to delete Customer&quot;,&quot;Customer deleted&quot;:&quot;Customer deleted&quot;,&quot;The driver id is required.&quot;:&quot;The driver id is required.&quot;,&quot;The selected driver does not exist.&quot;:&quot;The selected driver does not exist.&quot;,&quot;Error to Change Driver Status&quot;:&quot;Error to Change Driver Status&quot;,&quot;Driver Status changed&quot;:&quot;Driver Status changed&quot;,&quot;Can not find the selected Driver&quot;:&quot;Can not find the selected Driver&quot;,&quot;Error: can not save the Driver&quot;:&quot;Error: can not save the Driver&quot;,&quot;Driver saved successfully&quot;:&quot;Driver saved successfully&quot;,&quot;Error to delete Driver&quot;:&quot;Error to delete Driver&quot;,&quot;Driver deleted&quot;:&quot;Driver deleted&quot;,&quot;Drivers&quot;:&quot;Drivers&quot;,&quot;Active Drivers&quot;:&quot;Active Drivers&quot;,&quot;Pending Drivers&quot;:&quot;Pending Drivers&quot;,&quot;Blocked Drivers&quot;:&quot;Blocked Drivers&quot;,&quot;Unverified Drivers&quot;:&quot;Unverified Drivers&quot;,&quot;username&quot;:&quot;username&quot;,&quot;Add new Driver&quot;:&quot;Add new Driver&quot;,&quot;Username&quot;:&quot;Username&quot;,&quot;Team&quot;:&quot;Team&quot;,&quot;Select Team&quot;:&quot;Select Team&quot;,&quot;Driver Role&quot;:&quot;Driver Role&quot;,&quot;Home Address&quot;:&quot;Home Address&quot;,&quot;enter home address&quot;:&quot;enter home address&quot;,&quot;Select Commission Type&quot;:&quot;Select Commission Type&quot;,&quot;\\u064cRate&quot;:&quot;Rate&quot;,&quot;Fixed Amount&quot;:&quot;Fixed Amount&quot;,&quot;Subscription Monthly&quot;:&quot;Subscription Monthly&quot;,&quot;Commission Amount&quot;:&quot;Commission Amount&quot;,&quot;Vehicle Selection&quot;:&quot;Vehicle Selection&quot;,&quot;Wallets&quot;:&quot;Wallets&quot;,&quot;balance&quot;:&quot;balance&quot;,&quot;Debt Ceiling&quot;:&quot;Debt Ceiling&quot;,&quot;preview&quot;:&quot;preview&quot;,&quot;last transaction&quot;:&quot;last transaction&quot;,&quot;Amount&quot;:&quot;Amount&quot;,&quot;Maturity&quot;:&quot;Maturity&quot;,&quot;Task&quot;:&quot;Task&quot;,&quot;Add New Transaction&quot;:&quot;Add New Transaction&quot;,&quot;Enter the amount&quot;:&quot;Enter the amount&quot;,&quot;Transaction Type&quot;:&quot;Transaction Type&quot;,&quot;Credit&quot;:&quot;Credit&quot;,&quot;Debit&quot;:&quot;Debit&quot;,&quot;Maturity Time&quot;:&quot;Maturity Time&quot;,&quot;Optional notes...&quot;:&quot;Optional notes...&quot;,&quot;Image &quot;:&quot;Image &quot;,&quot;View the image&quot;:&quot;View the image&quot;,&quot;close&quot;:&quot;close&quot;,&quot;image&quot;:&quot;image&quot;}</span>\"
</pre><script>Sfdump(\"sf-dump-882621635\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1236</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [admin.index] not defined. at C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('admin.index', Array, true)
#1 C:\\xampp\\htdocs\\safedestssss\\resources\\views\\admin\\reports\\index.blade.php(96): route('admin.index')
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#5 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#7 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#9 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\EnsureUserIsAuthenticatedWithCorrectGuard.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\EnsureUserIsAuthenticatedWithCorrectGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'web')
#19 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\GlobalRateLimit.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GlobalRateLimit->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\LocaleMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [admin.index] not defined. at C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('admin.index', Array, true)
#1 C:\\xampp\\htdocs\\safedestssss\\storage\\framework\\views\\58cc6fb0fee7319fd1e665661251156d.php(94): route('admin.index')
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#5 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#7 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#9 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\EnsureUserIsAuthenticatedWithCorrectGuard.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\EnsureUserIsAuthenticatedWithCorrectGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'web')
#19 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\GlobalRateLimit.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GlobalRateLimit->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\LocaleMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 {main}
"} 
[2025-08-27 19:28:12] local.ERROR: Class "App\Models\Team" not found {"userId":2,"exception":"[object] (Error(code: 0): Class \"App\\Models\\Team\" not found at C:\\xampp\\htdocs\\safedestssss\\routes\\web.php:548)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#1 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(244): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(215): Illuminate\\Routing\\Route->runCallable()
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\EnsureUserIsAuthenticatedWithCorrectGuard.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\EnsureUserIsAuthenticatedWithCorrectGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'web')
#7 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\GlobalRateLimit.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GlobalRateLimit->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\LocaleMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-08-27 19:28:47] local.ERROR: Route [admin.index] not defined. {"view":{"view":"C:\\xampp\\htdocs\\safedestssss\\resources\\views\\admin\\reports\\customer-tasks.blade.php","data":{"jsTranslations":"<pre class=sf-dump id=sf-dump-1897723556 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"24393 characters\">{&quot;Dashboards&quot;:&quot;Dashboards&quot;,&quot;Dashboard&quot;:&quot;Dashboard&quot;,&quot;eCommerce&quot;:&quot;eCommerce&quot;,&quot;CRM&quot;:&quot;CRM&quot;,&quot;Layouts&quot;:&quot;Layouts&quot;,&quot;Collapsed menu&quot;:&quot;Collapsed menu&quot;,&quot;Content navbar&quot;:&quot;Content navbar&quot;,&quot;Content nav + Sidebar&quot;:&quot;Content nav + Sidebar&quot;,&quot;Horizontal&quot;:&quot;Horizontal&quot;,&quot;Vertical&quot;:&quot;Vertical&quot;,&quot;Without menu&quot;:&quot;Without menu&quot;,&quot;Without navbar&quot;:&quot;Without navbar&quot;,&quot;Fluid&quot;:&quot;Fluid&quot;,&quot;Container&quot;:&quot;Container&quot;,&quot;Blank&quot;:&quot;Blank&quot;,&quot;Laravel Example&quot;:&quot;Laravel Example&quot;,&quot;User Management&quot;:&quot;User Management&quot;,&quot;Apps&quot;:&quot;Apps&quot;,&quot;Email&quot;:&quot;Email&quot;,&quot;Chat&quot;:&quot;Chat&quot;,&quot;Calendar&quot;:&quot;Calendar&quot;,&quot;Kanban&quot;:&quot;Kanban&quot;,&quot;Products&quot;:&quot;Products&quot;,&quot;Add Product&quot;:&quot;Add Product&quot;,&quot;Product List&quot;:&quot;Product List&quot;,&quot;Category List&quot;:&quot;Category List&quot;,&quot;Category&quot;:&quot;Category&quot;,&quot;Order&quot;:&quot;Order&quot;,&quot;Order List&quot;:&quot;Order List&quot;,&quot;Order Details&quot;:&quot;Order Details&quot;,&quot;Customer&quot;:&quot;Customer&quot;,&quot;All Customer&quot;:&quot;All Customer&quot;,&quot;All Customers&quot;:&quot;All Customers&quot;,&quot;Customer Details&quot;:&quot;Customer Details&quot;,&quot;Overview&quot;:&quot;Overview&quot;,&quot;Address &amp; Billing&quot;:&quot;Address &amp; Billing&quot;,&quot;Manage Reviews&quot;:&quot;Manage Reviews&quot;,&quot;Referrals&quot;:&quot;Referrals&quot;,&quot;Settings&quot;:&quot;Settings&quot;,&quot;Store Details&quot;:&quot;Store Details&quot;,&quot;Payments&quot;:&quot;Payments&quot;,&quot;Shipping &amp; Delivery&quot;:&quot;Shipping &amp; Delivery&quot;,&quot;Locations&quot;:&quot;Locations&quot;,&quot;Roles &amp; Permissions&quot;:&quot;Roles &amp; Permissions&quot;,&quot;Add new roles with customized permissions as per your requirement&quot;:&quot;Add new roles with customized permissions as per your requirement&quot;,&quot;Add New Role&quot;:&quot;Add New Role&quot;,&quot;Edit Role&quot;:&quot;Edit Role&quot;,&quot;Role&quot;:&quot;Role&quot;,&quot;Created At&quot;:&quot;Created At&quot;,&quot;Actions&quot;:&quot;Actions&quot;,&quot;Search User&quot;:&quot;Search User&quot;,&quot;Displaying _START_ to _END_ of _TOTAL_ entries&quot;:&quot;Displaying _START_ to _END_ of _TOTAL_ entries&quot;,&quot;Showing _START_ to _END_ of _TOTAL_ entries&quot;:&quot;Showing _START_ to _END_ of _TOTAL_ entries&quot;,&quot;Search...&quot;:&quot;Search...&quot;,&quot;add new role&quot;:&quot;add new role&quot;,&quot;role&quot;:&quot;role&quot;,&quot;role name&quot;:&quot;role name&quot;,&quot;guard&quot;:&quot;guard&quot;,&quot;Administrator&quot;:&quot;Administrator&quot;,&quot;Driver&quot;:&quot;Driver&quot;,&quot;Details of&quot;:&quot;Details of&quot;,&quot;There is no Permissions found!&quot;:&quot;There is no Permissions found!&quot;,&quot;Error!! can not fiche any Permission&quot;:&quot;Error!! can not fiche any Permission&quot;,&quot;permissions&quot;:&quot;permissions&quot;,&quot;Roles&quot;:&quot;Roles&quot;,&quot;Role Name&quot;:&quot;Role Name&quot;,&quot;Guard&quot;:&quot;Guard&quot;,&quot;Permissions&quot;:&quot;Permissions&quot;,&quot;Home&quot;:&quot;Home&quot;,&quot;Profile&quot;:&quot;Profile&quot;,&quot;Messages&quot;:&quot;Messages&quot;,&quot;Close&quot;:&quot;Close&quot;,&quot;Submit&quot;:&quot;Submit&quot;,&quot;Users&quot;:&quot;Users&quot;,&quot;Active Users&quot;:&quot;Active Users&quot;,&quot;Inactive Users&quot;:&quot;Inactive Users&quot;,&quot;Pending Users&quot;:&quot;Pending Users&quot;,&quot;Add New User&quot;:&quot;Add New User&quot;,&quot;User&quot;:&quot;User&quot;,&quot;Phone&quot;:&quot;Phone&quot;,&quot;Status&quot;:&quot;Status&quot;,&quot;Reset Password&quot;:&quot;Reset Password&quot;,&quot;Add new User&quot;:&quot;Add new User&quot;,&quot;Edit User&quot;:&quot;Edit User&quot;,&quot;Main&quot;:&quot;Main&quot;,&quot;Additional&quot;:&quot;Additional&quot;,&quot;Full Name&quot;:&quot;Full Name&quot;,&quot;<EMAIL>&quot;:&quot;<EMAIL>&quot;,&quot;Enter phone number&quot;:&quot;Enter phone number&quot;,&quot;Password&quot;:&quot;Password&quot;,&quot;Confirm Password&quot;:&quot;Confirm Password&quot;,&quot;User Role&quot;:&quot;User Role&quot;,&quot;Teams&quot;:&quot;Teams&quot;,&quot;Customers&quot;:&quot;Customers&quot;,&quot;Active Customers&quot;:&quot;Active Customers&quot;,&quot;Unverified Customers&quot;:&quot;Unverified Customers&quot;,&quot;Blocked Customers&quot;:&quot;Blocked Customers&quot;,&quot;name&quot;:&quot;name&quot;,&quot;email&quot;:&quot;email&quot;,&quot;phone&quot;:&quot;phone&quot;,&quot;tags&quot;:&quot;tags&quot;,&quot;status&quot;:&quot;status&quot;,&quot;created at&quot;:&quot;created at&quot;,&quot;actions&quot;:&quot;actions&quot;,&quot;Add New Customer&quot;:&quot;Add New Customer&quot;,&quot;Customer Role&quot;:&quot;Customer Role&quot;,&quot;Select Role&quot;:&quot;Select Role&quot;,&quot;Company Info&quot;:&quot;Company Info&quot;,&quot;Company Name&quot;:&quot;Company Name&quot;,&quot;enter company name&quot;:&quot;enter company name&quot;,&quot;Company Address&quot;:&quot;Company Address&quot;,&quot;enter company address&quot;:&quot;enter company address&quot;,&quot;Tags&quot;:&quot;Tags&quot;,&quot;Select Template&quot;:&quot;Select Template&quot;,&quot;-- Select Template&quot;:&quot;-- Select Template&quot;,&quot;--- Select Template&quot;:&quot;--- Select Template&quot;,&quot;Logistics&quot;:&quot;Logistics&quot;,&quot;Fleet&quot;:&quot;Fleet&quot;,&quot;Invoice&quot;:&quot;Invoice&quot;,&quot;Preview&quot;:&quot;Preview&quot;,&quot;Add&quot;:&quot;Add&quot;,&quot;Pages&quot;:&quot;Pages&quot;,&quot;User Profile&quot;:&quot;User Profile&quot;,&quot;Projects&quot;:&quot;Projects&quot;,&quot;Account Settings&quot;:&quot;Account Settings&quot;,&quot;Account&quot;:&quot;Account&quot;,&quot;Security&quot;:&quot;Security&quot;,&quot;Billing &amp; Plans&quot;:&quot;Billing &amp; Plans&quot;,&quot;Notifications&quot;:&quot;Notifications&quot;,&quot;Connections&quot;:&quot;Connections&quot;,&quot;FAQ&quot;:&quot;FAQ&quot;,&quot;Front Pages&quot;:&quot;Front Pages&quot;,&quot;Payment&quot;:&quot;Payment&quot;,&quot;Help Center&quot;:&quot;Help Center&quot;,&quot;Landing&quot;:&quot;Landing&quot;,&quot;Categories&quot;:&quot;Categories&quot;,&quot;Article&quot;:&quot;Article&quot;,&quot;Pricing&quot;:&quot;Pricing&quot;,&quot;Error&quot;:&quot;Error&quot;,&quot;Coming Soon&quot;:&quot;Coming Soon&quot;,&quot;Under Maintenance&quot;:&quot;Under Maintenance&quot;,&quot;Not Authorized&quot;:&quot;Not Authorized&quot;,&quot;Authentications&quot;:&quot;Authentications&quot;,&quot;Login&quot;:&quot;Login&quot;,&quot;Register&quot;:&quot;Register&quot;,&quot;Verify Email&quot;:&quot;Verify Email&quot;,&quot;Forgot Password&quot;:&quot;Forgot Password&quot;,&quot;Two Steps&quot;:&quot;Two Steps&quot;,&quot;Basic&quot;:&quot;Basic&quot;,&quot;Cover&quot;:&quot;Cover&quot;,&quot;Multi-steps&quot;:&quot;Multi-steps&quot;,&quot;Modal Examples&quot;:&quot;Modal Examples&quot;,&quot;Wizard Examples&quot;:&quot;Wizard Examples&quot;,&quot;Checkout&quot;:&quot;Checkout&quot;,&quot;Property Listing&quot;:&quot;Property Listing&quot;,&quot;Create Deal&quot;:&quot;Create Deal&quot;,&quot;Icons&quot;:&quot;Icons&quot;,&quot;Tabler&quot;:&quot;Tabler&quot;,&quot;Fontawesome&quot;:&quot;Fontawesome&quot;,&quot;User interface&quot;:&quot;User interface&quot;,&quot;Accordion&quot;:&quot;Accordion&quot;,&quot;Alerts&quot;:&quot;Alerts&quot;,&quot;App Brand&quot;:&quot;App Brand&quot;,&quot;Badges&quot;:&quot;Badges&quot;,&quot;Buttons&quot;:&quot;Buttons&quot;,&quot;Cards&quot;:&quot;Cards&quot;,&quot;Advance&quot;:&quot;Advance&quot;,&quot;Statistics&quot;:&quot;Statistics&quot;,&quot;Analytics&quot;:&quot;Analytics&quot;,&quot;Carousel&quot;:&quot;Carousel&quot;,&quot;Collapse&quot;:&quot;Collapse&quot;,&quot;Dropdowns&quot;:&quot;Dropdowns&quot;,&quot;Footer&quot;:&quot;Footer&quot;,&quot;List Groups&quot;:&quot;List Groups&quot;,&quot;Modals&quot;:&quot;Modals&quot;,&quot;Menu&quot;:&quot;Menu&quot;,&quot;Navbar&quot;:&quot;Navbar&quot;,&quot;Offcanvas&quot;:&quot;Offcanvas&quot;,&quot;Pagination &amp; Breadcrumbs&quot;:&quot;Pagination &amp; Breadcrumbs&quot;,&quot;Progress&quot;:&quot;Progress&quot;,&quot;Spinners&quot;:&quot;Spinners&quot;,&quot;Tabs &amp; Pills&quot;:&quot;Tabs &amp; Pills&quot;,&quot;Toasts&quot;:&quot;Toasts&quot;,&quot;Tooltips &amp; Popovers&quot;:&quot;Tooltips &amp; Popovers&quot;,&quot;Typography&quot;:&quot;Typography&quot;,&quot;Extended UI&quot;:&quot;Extended UI&quot;,&quot;Avatar&quot;:&quot;Avatar&quot;,&quot;BlockUI&quot;:&quot;BlockUI&quot;,&quot;Drag &amp; Drop&quot;:&quot;Drag &amp; Drop&quot;,&quot;Media Player&quot;:&quot;Media Player&quot;,&quot;Perfect Scrollbar&quot;:&quot;Perfect Scrollbar&quot;,&quot;Star Ratings&quot;:&quot;Star Ratings&quot;,&quot;SweetAlert2&quot;:&quot;SweetAlert2&quot;,&quot;Text Divider&quot;:&quot;Text Divider&quot;,&quot;Timeline&quot;:&quot;Timeline&quot;,&quot;Fullscreen&quot;:&quot;Fullscreen&quot;,&quot;Tour&quot;:&quot;Tour&quot;,&quot;Treeview&quot;:&quot;Treeview&quot;,&quot;Miscellaneous&quot;:&quot;Miscellaneous&quot;,&quot;Misc&quot;:&quot;Misc&quot;,&quot;Form Elements&quot;:&quot;Form Elements&quot;,&quot;Basic Inputs&quot;:&quot;Basic Inputs&quot;,&quot;Input groups&quot;:&quot;Input groups&quot;,&quot;Custom Options&quot;:&quot;Custom Options&quot;,&quot;Editors&quot;:&quot;Editors&quot;,&quot;File Upload&quot;:&quot;File Upload&quot;,&quot;Pickers&quot;:&quot;Pickers&quot;,&quot;Select &amp; Tags&quot;:&quot;Select &amp; Tags&quot;,&quot;Sliders&quot;:&quot;Sliders&quot;,&quot;Switches&quot;:&quot;Switches&quot;,&quot;Extras&quot;:&quot;Extras&quot;,&quot;Form Layouts&quot;:&quot;Form Layouts&quot;,&quot;Vertical Form&quot;:&quot;Vertical Form&quot;,&quot;Horizontal Form&quot;:&quot;Horizontal Form&quot;,&quot;Sticky Actions&quot;:&quot;Sticky Actions&quot;,&quot;Form Wizard&quot;:&quot;Form Wizard&quot;,&quot;Numbered&quot;:&quot;Numbered&quot;,&quot;Advanced&quot;:&quot;Advanced&quot;,&quot;Forms&quot;:&quot;Forms&quot;,&quot;Form Validation&quot;:&quot;Form Validation&quot;,&quot;Tables&quot;:&quot;Tables&quot;,&quot;Datatables&quot;:&quot;Datatables&quot;,&quot;Extensions&quot;:&quot;Extensions&quot;,&quot;Charts&quot;:&quot;Charts&quot;,&quot;Apex Charts&quot;:&quot;Apex Charts&quot;,&quot;ChartJS&quot;:&quot;ChartJS&quot;,&quot;Leaflet Maps&quot;:&quot;Leaflet Maps&quot;,&quot;Support&quot;:&quot;Support&quot;,&quot;Documentation&quot;:&quot;Documentation&quot;,&quot;Academy&quot;:&quot;Academy&quot;,&quot;My Course&quot;:&quot;My Course&quot;,&quot;Course Details&quot;:&quot;Course Details&quot;,&quot;Apps &amp; Pages&quot;:&quot;Apps &amp; Pages&quot;,&quot;Components&quot;:&quot;Components&quot;,&quot;Forms &amp; Tables&quot;:&quot;Forms &amp; Tables&quot;,&quot;Charts &amp; Maps&quot;:&quot;Charts &amp; Maps&quot;,&quot;Id&quot;:&quot;Id&quot;,&quot;General&quot;:&quot;General&quot;,&quot;Template&quot;:&quot;Template&quot;,&quot;Donut drag\\u00e9e jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.&quot;:&quot;Donut drag\\u00e9e jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.&quot;,&quot;Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.&quot;:&quot;Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.&quot;,&quot;Geo-fence&quot;:&quot;Geo-fence&quot;,&quot;It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.&quot;:&quot;It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.&quot;,&quot;Add New Geo-fence&quot;:&quot;Add New Geo-fence&quot;,&quot;\\ud83d\\udd0d Search Team&quot;:&quot;\\ud83d\\udd0d Search Team&quot;,&quot;Geofences&quot;:&quot;Geofences&quot;,&quot;Add Geo-fence&quot;:&quot;Add Geo-fence&quot;,&quot;Select Teams&quot;:&quot;Select Teams&quot;,&quot;Name&quot;:&quot;Name&quot;,&quot;Enter name&quot;:&quot;Enter name&quot;,&quot;Description&quot;:&quot;Description&quot;,&quot;Enter description&quot;:&quot;Enter description&quot;,&quot;The role name is required.&quot;:&quot;The role name is required.&quot;,&quot;The role name has already been taken.&quot;:&quot;The role name has already been taken.&quot;,&quot;The guard field is required.&quot;:&quot;The guard field is required.&quot;,&quot;The selected guard is invalid.&quot;:&quot;The selected guard is invalid.&quot;,&quot;At least one permission must be selected.&quot;:&quot;At least one permission must be selected.&quot;,&quot;Permissions must be an array.&quot;:&quot;Permissions must be an array.&quot;,&quot;Error to Save Role&quot;:&quot;Error to Save Role&quot;,&quot;Role Saved&quot;:&quot;Role Saved&quot;,&quot;This role can not be deleted&quot;:&quot;This role can not be deleted&quot;,&quot;There are users connected with this role&quot;:&quot;There are users connected with this role&quot;,&quot;Error to delete role&quot;:&quot;Error to delete role&quot;,&quot;Role deleted&quot;:&quot;Role deleted&quot;,&quot;Rate&quot;:&quot;Rate&quot;,&quot;Fixed&quot;:&quot;Fixed&quot;,&quot;Commission Rate&quot;:&quot;Commission Rate&quot;,&quot;Commission fixed Amount&quot;:&quot;Commission fixed Amount&quot;,&quot;General Settings&quot;:&quot;General Settings&quot;,&quot;You can manage the main and vital settings of the platform from here, so be careful.&quot;:&quot;You can manage the main and vital settings of the platform from here, so be careful.&quot;,&quot;Templates&quot;:&quot;Templates&quot;,&quot;Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more&quot;:&quot;Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more&quot;,&quot;Add New Template&quot;:&quot;Add New Template&quot;,&quot;Template Name&quot;:&quot;Template Name&quot;,&quot;enter the Template name&quot;:&quot;enter the Template name&quot;,&quot;Default Customer Template&quot;:&quot;Default Customer Template&quot;,&quot;Default Driver Template&quot;:&quot;Default Driver Template&quot;,&quot;Default User Template&quot;:&quot;Default User Template&quot;,&quot;Default Task Template&quot;:&quot;Default Task Template&quot;,&quot;Drivers Commission&quot;:&quot;Drivers Commission&quot;,&quot;Commission Type&quot;:&quot;Commission Type&quot;,&quot;The user id is required.&quot;:&quot;The user id is required.&quot;,&quot;The selected user does not exist.&quot;:&quot;The selected user does not exist.&quot;,&quot;The status field is required.&quot;:&quot;The status field is required.&quot;,&quot;Error to Change user Status&quot;:&quot;Error to Change user Status&quot;,&quot;User Status changed&quot;:&quot;User Status changed&quot;,&quot;The name field is required.&quot;:&quot;The name field is required.&quot;,&quot;The email field is required.&quot;:&quot;The email field is required.&quot;,&quot;The email has already been taken.&quot;:&quot;The email has already been taken.&quot;,&quot;The phone field is required.&quot;:&quot;The phone field is required.&quot;,&quot;The phone has already been taken.&quot;:&quot;The phone has already been taken.&quot;,&quot;The password field is required.&quot;:&quot;The password field is required.&quot;,&quot;The password and confirmation must match.&quot;:&quot;The password and confirmation must match.&quot;,&quot;The user role is required.&quot;:&quot;The user role is required.&quot;,&quot;The selected role is invalid.&quot;:&quot;The selected role is invalid.&quot;,&quot;Teams must be an array.&quot;:&quot;Teams must be an array.&quot;,&quot;Customers must be an array.&quot;:&quot;Customers must be an array.&quot;,&quot;The selected template is invalid.&quot;:&quot;The selected template is invalid.&quot;,&quot;The :label field is required.&quot;:&quot;The :label field is required.&quot;,&quot;Can not find the selected user&quot;:&quot;Can not find the selected user&quot;,&quot;User saved successfully&quot;:&quot;User saved successfully&quot;,&quot;User not found&quot;:&quot;User not found&quot;,&quot;Error to change reset password  status&quot;:&quot;Error to change reset password  status&quot;,&quot;User deleted&quot;:&quot;User deleted&quot;,&quot;The selected User has teams to mange. you can not delete hem right now&quot;:&quot;The selected User has teams to mange. you can not delete hem right now&quot;,&quot;Error to delete User&quot;:&quot;Error to delete User&quot;,&quot;Vehicles&quot;:&quot;Vehicles&quot;,&quot;Managing the types of vehicles and trucks that will provide delivery services on the platform&quot;:&quot;Managing the types of vehicles and trucks that will provide delivery services on the platform&quot;,&quot;Vehicles Types&quot;:&quot;Vehicles Types&quot;,&quot;Vehicles Sizes&quot;:&quot;Vehicles Sizes&quot;,&quot;vehicle name&quot;:&quot;vehicle name&quot;,&quot;English name&quot;:&quot;English name&quot;,&quot;save&quot;:&quot;save&quot;,&quot;types&quot;:&quot;types&quot;,&quot;Select vehicle&quot;:&quot;Select vehicle&quot;,&quot;select vehicle&quot;:&quot;select vehicle&quot;,&quot;Flitter by vehicle&quot;:&quot;Flitter by vehicle&quot;,&quot;all vehicle&quot;:&quot;all vehicle&quot;,&quot;vehicle&quot;:&quot;vehicle&quot;,&quot;type name&quot;:&quot;type name&quot;,&quot;sizes&quot;:&quot;sizes&quot;,&quot;size&quot;:&quot;size&quot;,&quot;Select vehicle Type&quot;:&quot;Select vehicle Type&quot;,&quot;Flitter by vehicle type&quot;:&quot;Flitter by vehicle type&quot;,&quot;select vehicle type&quot;:&quot;select vehicle type&quot;,&quot;vehicle type&quot;:&quot;vehicle type&quot;,&quot;No data available&quot;:&quot;No data available&quot;,&quot;select vehicle Size&quot;:&quot;select vehicle Size&quot;,&quot;Add New Tag&quot;:&quot;Add New Tag&quot;,&quot;tag name&quot;:&quot;tag name&quot;,&quot;enter the tag name&quot;:&quot;enter the tag name&quot;,&quot;tag slug&quot;:&quot;tag slug&quot;,&quot;enter the tag slug&quot;:&quot;enter the tag slug&quot;,&quot;Select Tags&quot;:&quot;Select Tags&quot;,&quot;The tag name is required.&quot;:&quot;The tag name is required.&quot;,&quot;The tag name has already been taken.&quot;:&quot;The tag name has already been taken.&quot;,&quot;The tag slug is required.&quot;:&quot;The tag slug is required.&quot;,&quot;The tag slug has already been taken.&quot;:&quot;The tag slug has already been taken.&quot;,&quot;The description must be a string.&quot;:&quot;The description must be a string.&quot;,&quot;The description may not be greater than 400 characters.&quot;:&quot;The description may not be greater than 400 characters.&quot;,&quot;Can not find the selected Tag&quot;:&quot;Can not find the selected Tag&quot;,&quot;Error: can not save the Tag&quot;:&quot;Error: can not save the Tag&quot;,&quot;Tag saved successfully&quot;:&quot;Tag saved successfully&quot;,&quot;Error to find selected Tag&quot;:&quot;Error to find selected Tag&quot;,&quot;Error to delete Tag&quot;:&quot;Error to delete Tag&quot;,&quot;Tag deleted&quot;:&quot;Tag deleted&quot;,&quot;The geofence name is required.&quot;:&quot;The geofence name is required.&quot;,&quot;The geofence name has already been taken.&quot;:&quot;The geofence name has already been taken.&quot;,&quot;The coordinates field is required.&quot;:&quot;The coordinates field is required.&quot;,&quot;The coordinates must be a string.&quot;:&quot;The coordinates must be a string.&quot;,&quot;Can not find the selected Geo-Fence&quot;:&quot;Can not find the selected Geo-Fence&quot;,&quot;error to save Geo-Fence&quot;:&quot;error to save Geo-Fence&quot;,&quot;Geo-Fence saved successfully&quot;:&quot;Geo-Fence saved successfully&quot;,&quot;Error to delete Geo-fence&quot;:&quot;Error to delete Geo-fence&quot;,&quot;Geo-fence deleted&quot;:&quot;Geo-fence deleted&quot;,&quot;Points&quot;:&quot;Points&quot;,&quot;Add New Point&quot;:&quot;Add New Point&quot;,&quot;address&quot;:&quot;address&quot;,&quot;customer&quot;:&quot;customer&quot;,&quot;enter the point name&quot;:&quot;enter the point name&quot;,&quot;enter the point address&quot;:&quot;enter the point address&quot;,&quot;Location&quot;:&quot;Location&quot;,&quot;confirm location&quot;:&quot;confirm location&quot;,&quot;Contact name&quot;:&quot;Contact name&quot;,&quot;enter the point contact name&quot;:&quot;enter the point contact name&quot;,&quot;Contact phone&quot;:&quot;Contact phone&quot;,&quot;enter the point contact phone&quot;:&quot;enter the point contact phone&quot;,&quot;Select Customer&quot;:&quot;Select Customer&quot;,&quot;The point name is required.&quot;:&quot;The point name is required.&quot;,&quot;The point name must be a string.&quot;:&quot;The point name must be a string.&quot;,&quot;The contact name must be a string.&quot;:&quot;The contact name must be a string.&quot;,&quot;The contact name may not be greater than 400 characters.&quot;:&quot;The contact name may not be greater than 400 characters.&quot;,&quot;The contact phone must be a string.&quot;:&quot;The contact phone must be a string.&quot;,&quot;The contact phone may not be greater than 50 characters.&quot;:&quot;The contact phone may not be greater than 50 characters.&quot;,&quot;The address field is required.&quot;:&quot;The address field is required.&quot;,&quot;The address must be a string.&quot;:&quot;The address must be a string.&quot;,&quot;The address may not be greater than 500 characters.&quot;:&quot;The address may not be greater than 500 characters.&quot;,&quot;The latitude field is required.&quot;:&quot;The latitude field is required.&quot;,&quot;The latitude must be a number.&quot;:&quot;The latitude must be a number.&quot;,&quot;The longitude field is required.&quot;:&quot;The longitude field is required.&quot;,&quot;The longitude must be a number.&quot;:&quot;The longitude must be a number.&quot;,&quot;The selected customer is invalid.&quot;:&quot;The selected customer is invalid.&quot;,&quot;Can not find the selected Point&quot;:&quot;Can not find the selected Point&quot;,&quot;Error: can not save the Point&quot;:&quot;Error: can not save the Point&quot;,&quot;Point saved successfully&quot;:&quot;Point saved successfully&quot;,&quot;Error to delete Point. its connect with pricing mater&quot;:&quot;Error to delete Point. its connect with pricing mater&quot;,&quot;Error to delete Point&quot;:&quot;Error to delete Point&quot;,&quot;Point deleted&quot;:&quot;Point deleted&quot;,&quot;Blockages&quot;:&quot;Blockages&quot;,&quot;Add a new Blockage&quot;:&quot;Add a new Blockage&quot;,&quot;type&quot;:&quot;type&quot;,&quot;description&quot;:&quot;description&quot;,&quot;coordinates&quot;:&quot;coordinates&quot;,&quot;Block Type&quot;:&quot;Block Type&quot;,&quot;Select Type&quot;:&quot;Select Type&quot;,&quot;Point Closed&quot;:&quot;Point Closed&quot;,&quot;Line Closed&quot;:&quot;Line Closed&quot;,&quot;Block Description&quot;:&quot;Block Description&quot;,&quot;optional&quot;:&quot;optional&quot;,&quot;draw the Points on the map&quot;:&quot;draw the Points on the map&quot;,&quot;Save&quot;:&quot;Save&quot;,&quot;Cancel&quot;:&quot;Cancel&quot;,&quot;The blockage type is required.&quot;:&quot;The blockage type is required.&quot;,&quot;The selected blockage type is invalid.&quot;:&quot;The selected blockage type is invalid.&quot;,&quot;Error: can not save the Blockage&quot;:&quot;Error: can not save the Blockage&quot;,&quot;Blockage saved successfully&quot;:&quot;Blockage saved successfully&quot;,&quot;Error to delete Blockage&quot;:&quot;Error to delete Blockage&quot;,&quot;Blockage deleted&quot;:&quot;Blockage deleted&quot;,&quot;Pricing Methods&quot;:&quot;Pricing Methods&quot;,&quot;Method&quot;:&quot;Method&quot;,&quot;Add New Method&quot;:&quot;Add New Method&quot;,&quot;Method Name&quot;:&quot;Method Name&quot;,&quot;enter the Method name&quot;:&quot;enter the Method name&quot;,&quot;Edit Method&quot;:&quot;Edit Method&quot;,&quot;The method name is required.&quot;:&quot;The method name is required.&quot;,&quot;The method name has already been taken.&quot;:&quot;The method name has already been taken.&quot;,&quot;The description field is required.&quot;:&quot;The description field is required.&quot;,&quot;Can not find the selected Pricing Method&quot;:&quot;Can not find the selected Pricing Method&quot;,&quot;error to save Pricing Method&quot;:&quot;error to save Pricing Method&quot;,&quot;Pricing Method saved successfully&quot;:&quot;Pricing Method saved successfully&quot;,&quot;Pricing Method not found&quot;:&quot;Pricing Method not found&quot;,&quot;Error to change Pricing Method status&quot;:&quot;Error to change Pricing Method status&quot;,&quot;label&quot;:&quot;label&quot;,&quot;driver can&quot;:&quot;driver can&quot;,&quot;customer can&quot;:&quot;customer can&quot;,&quot;value&quot;:&quot;value&quot;,&quot;require&quot;:&quot;require&quot;,&quot;Select Values&quot;:&quot;Select Values&quot;,&quot;Value&quot;:&quot;Value&quot;,&quot;Display Name&quot;:&quot;Display Name&quot;,&quot;Action&quot;:&quot;Action&quot;,&quot;More&quot;:&quot;More&quot;,&quot;Customers Selections&quot;:&quot;Customers Selections&quot;,&quot;Apply to All Customers&quot;:&quot;Apply to All Customers&quot;,&quot;Use customers tags&quot;:&quot;Use customers tags&quot;,&quot;Use Specific customers&quot;:&quot;Use Specific customers&quot;,&quot;Vehicles Selections&quot;:&quot;Vehicles Selections&quot;,&quot;Base Fare&quot;:&quot;Base Fare&quot;,&quot;Base Distance&quot;:&quot;Base Distance&quot;,&quot;Base Waiting&quot;:&quot;Base Waiting&quot;,&quot;Distance Fare&quot;:&quot;Distance Fare&quot;,&quot;Waiting Fare&quot;:&quot;Waiting Fare&quot;,&quot;Dynamic Pricing Based on Field Values&quot;:&quot;Dynamic Pricing Based on Field Values&quot;,&quot;add field&quot;:&quot;add field&quot;,&quot;Dynamic Pricing Based on Geo-fence&quot;:&quot;Dynamic Pricing Based on Geo-fence&quot;,&quot;add geofence&quot;:&quot;add geofence&quot;,&quot;Commission&quot;:&quot;Commission&quot;,&quot;VAT Commission&quot;:&quot;VAT Commission&quot;,&quot;Service Tax Commission&quot;:&quot;Service Tax Commission&quot;,&quot;Discount Fare&quot;:&quot;Discount Fare&quot;,&quot;Discount percentage %&quot;:&quot;Discount percentage %&quot;,&quot;The rule name is required.&quot;:&quot;The rule name is required.&quot;,&quot;The rule name must be a string.&quot;:&quot;The rule name must be a string.&quot;,&quot;The rule name may not be greater than 255 characters.&quot;:&quot;The rule name may not be greater than 255 characters.&quot;,&quot;The rule name has already been taken.&quot;:&quot;The rule name has already been taken.&quot;,&quot;The decimal places field is required.&quot;:&quot;The decimal places field is required.&quot;,&quot;The decimal places must be an integer.&quot;:&quot;The decimal places must be an integer.&quot;,&quot;The decimal places must be at least 0.&quot;:&quot;The decimal places must be at least 0.&quot;,&quot;The decimal places may not be greater than 10.&quot;:&quot;The decimal places may not be greater than 10.&quot;,&quot;The form template is required.&quot;:&quot;The form template is required.&quot;,&quot;The form template id must be an integer.&quot;:&quot;The form template id must be an integer.&quot;,&quot;The selected form template is invalid.&quot;:&quot;The selected form template is invalid.&quot;,&quot;At least one customer must be selected.&quot;:&quot;At least one customer must be selected.&quot;,&quot;Each customer is required.&quot;:&quot;Each customer is required.&quot;,&quot;Each customer id must be an integer.&quot;:&quot;Each customer id must be an integer.&quot;,&quot;At least one tag must be selected.&quot;:&quot;At least one tag must be selected.&quot;,&quot;Tags must be an array.&quot;:&quot;Tags must be an array.&quot;,&quot;Each tag is required.&quot;:&quot;Each tag is required.&quot;,&quot;Each tag id must be an integer.&quot;:&quot;Each tag id must be an integer.&quot;,&quot;The selected tag is invalid.&quot;:&quot;The selected tag is invalid.&quot;,&quot;At least one vehicle size must be selected.&quot;:&quot;At least one vehicle size must be selected.&quot;,&quot;Sizes must be an array.&quot;:&quot;Sizes must be an array.&quot;,&quot;Each size id must be an integer.&quot;:&quot;Each size id must be an integer.&quot;,&quot;The selected vehicle size is invalid.&quot;:&quot;The selected vehicle size is invalid.&quot;,&quot;The base fare field is required.&quot;:&quot;The base fare field is required.&quot;,&quot;The base fare must be a number.&quot;:&quot;The base fare must be a number.&quot;,&quot;The base fare must be at least 0.&quot;:&quot;The base fare must be at least 0.&quot;,&quot;The base distance field is required.&quot;:&quot;The base distance field is required.&quot;,&quot;The base distance must be a number.&quot;:&quot;The base distance must be a number.&quot;,&quot;The base distance must be at least 0.&quot;:&quot;The base distance must be at least 0.&quot;,&quot;The base waiting field is required.&quot;:&quot;The base waiting field is required.&quot;,&quot;The base waiting must be a number.&quot;:&quot;The base waiting must be a number.&quot;,&quot;The base waiting must be at least 0.&quot;:&quot;The base waiting must be at least 0.&quot;,&quot;The distance fare field is required.&quot;:&quot;The distance fare field is required.&quot;,&quot;The distance fare must be a number.&quot;:&quot;The distance fare must be a number.&quot;,&quot;The distance fare must be at least 0.&quot;:&quot;The distance fare must be at least 0.&quot;,&quot;The waiting fare field is required.&quot;:&quot;The waiting fare field is required.&quot;,&quot;The waiting fare must be a number.&quot;:&quot;The waiting fare must be a number.&quot;,&quot;The waiting fare must be at least 0.&quot;:&quot;The waiting fare must be at least 0.&quot;,&quot;The VAT commission field is required.&quot;:&quot;The VAT commission field is required.&quot;,&quot;The VAT commission must be a number.&quot;:&quot;The VAT commission must be a number.&quot;,&quot;The VAT commission must be at least 0.&quot;:&quot;The VAT commission must be at least 0.&quot;,&quot;The VAT commission may not be greater than 100.&quot;:&quot;The VAT commission may not be greater than 100.&quot;,&quot;The service commission field is required.&quot;:&quot;The service commission field is required.&quot;,&quot;The service commission must be a number.&quot;:&quot;The service commission must be a number.&quot;,&quot;The service commission must be at least 0.&quot;:&quot;The service commission must be at least 0.&quot;,&quot;The service commission may not be greater than 100.&quot;:&quot;The service commission may not be greater than 100.&quot;,&quot;The discount must be a number.&quot;:&quot;The discount must be a number.&quot;,&quot;The discount must be at least 0.&quot;:&quot;The discount must be at least 0.&quot;,&quot;The discount may not be greater than 100.&quot;:&quot;The discount may not be greater than 100.&quot;,&quot;Task Done&quot;:&quot;Task Done&quot;,&quot;Running Tasks&quot;:&quot;Running Tasks&quot;,&quot;Details&quot;:&quot;Details&quot;,&quot;Edit&quot;:&quot;Edit&quot;,&quot;Suspend&quot;:&quot;Suspend&quot;,&quot;Tasks&quot;:&quot;Tasks&quot;,&quot;Total&quot;:&quot;Total&quot;,&quot;Issued Date&quot;:&quot;Issued Date&quot;,&quot;Additional Fields&quot;:&quot;Additional Fields&quot;,&quot;No additional data found for this customer.&quot;:&quot;No additional data found for this customer.&quot;,&quot;View&quot;:&quot;View&quot;,&quot;Change Status&quot;:&quot;Change Status&quot;,&quot;Create Wallet&quot;:&quot;Create Wallet&quot;,&quot;The customer id is required.&quot;:&quot;The customer id is required.&quot;,&quot;The selected customer does not exist.&quot;:&quot;The selected customer does not exist.&quot;,&quot;You do not have permission to do actions to this record&quot;:&quot;You do not have permission to do actions to this record&quot;,&quot;Error to Change Customer Status&quot;:&quot;Error to Change Customer Status&quot;,&quot;Customer Status changed&quot;:&quot;Customer Status changed&quot;,&quot;Can not find the selected Customer&quot;:&quot;Can not find the selected Customer&quot;,&quot;Error: can not save the Customer&quot;:&quot;Error: can not save the Customer&quot;,&quot;Customer saved successfully&quot;:&quot;Customer saved successfully&quot;,&quot;Error to delete Customer&quot;:&quot;Error to delete Customer&quot;,&quot;Customer deleted&quot;:&quot;Customer deleted&quot;,&quot;The driver id is required.&quot;:&quot;The driver id is required.&quot;,&quot;The selected driver does not exist.&quot;:&quot;The selected driver does not exist.&quot;,&quot;Error to Change Driver Status&quot;:&quot;Error to Change Driver Status&quot;,&quot;Driver Status changed&quot;:&quot;Driver Status changed&quot;,&quot;Can not find the selected Driver&quot;:&quot;Can not find the selected Driver&quot;,&quot;Error: can not save the Driver&quot;:&quot;Error: can not save the Driver&quot;,&quot;Driver saved successfully&quot;:&quot;Driver saved successfully&quot;,&quot;Error to delete Driver&quot;:&quot;Error to delete Driver&quot;,&quot;Driver deleted&quot;:&quot;Driver deleted&quot;,&quot;Drivers&quot;:&quot;Drivers&quot;,&quot;Active Drivers&quot;:&quot;Active Drivers&quot;,&quot;Pending Drivers&quot;:&quot;Pending Drivers&quot;,&quot;Blocked Drivers&quot;:&quot;Blocked Drivers&quot;,&quot;Unverified Drivers&quot;:&quot;Unverified Drivers&quot;,&quot;username&quot;:&quot;username&quot;,&quot;Add new Driver&quot;:&quot;Add new Driver&quot;,&quot;Username&quot;:&quot;Username&quot;,&quot;Team&quot;:&quot;Team&quot;,&quot;Select Team&quot;:&quot;Select Team&quot;,&quot;Driver Role&quot;:&quot;Driver Role&quot;,&quot;Home Address&quot;:&quot;Home Address&quot;,&quot;enter home address&quot;:&quot;enter home address&quot;,&quot;Select Commission Type&quot;:&quot;Select Commission Type&quot;,&quot;\\u064cRate&quot;:&quot;Rate&quot;,&quot;Fixed Amount&quot;:&quot;Fixed Amount&quot;,&quot;Subscription Monthly&quot;:&quot;Subscription Monthly&quot;,&quot;Commission Amount&quot;:&quot;Commission Amount&quot;,&quot;Vehicle Selection&quot;:&quot;Vehicle Selection&quot;,&quot;Wallets&quot;:&quot;Wallets&quot;,&quot;balance&quot;:&quot;balance&quot;,&quot;Debt Ceiling&quot;:&quot;Debt Ceiling&quot;,&quot;preview&quot;:&quot;preview&quot;,&quot;last transaction&quot;:&quot;last transaction&quot;,&quot;Amount&quot;:&quot;Amount&quot;,&quot;Maturity&quot;:&quot;Maturity&quot;,&quot;Task&quot;:&quot;Task&quot;,&quot;Add New Transaction&quot;:&quot;Add New Transaction&quot;,&quot;Enter the amount&quot;:&quot;Enter the amount&quot;,&quot;Transaction Type&quot;:&quot;Transaction Type&quot;,&quot;Credit&quot;:&quot;Credit&quot;,&quot;Debit&quot;:&quot;Debit&quot;,&quot;Maturity Time&quot;:&quot;Maturity Time&quot;,&quot;Optional notes...&quot;:&quot;Optional notes...&quot;,&quot;Image &quot;:&quot;Image &quot;,&quot;View the image&quot;:&quot;View the image&quot;,&quot;close&quot;:&quot;close&quot;,&quot;image&quot;:&quot;image&quot;}</span>\"
</pre><script>Sfdump(\"sf-dump-1897723556\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-2103645023 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1237</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-2103645023\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","customers":"<pre class=sf-dump id=sf-dump-1348821624 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2263</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Customer
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref>#2264</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">customers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Customer
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref>#2265</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">customers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Customer
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref>#2266</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">customers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Customer
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref>#2267</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">customers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Customer
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref>#2268</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">customers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Customer
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref>#2269</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">customers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Customer
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref>#2270</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">customers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Customer
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref>#2271</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">customers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Customer
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref>#2272</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">customers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>9</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Customer
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref>#2273</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">customers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Customer`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1348821624\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","drivers":"<pre class=sf-dump id=sf-dump-1623182820 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2244</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Driver
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Driver</span></span> {<a class=sf-dump-ref>#2240</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">drivers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:31</span> [ &#8230;31]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"6 characters\">driver</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Driver`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Driver`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Driver`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Driver`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Driver
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Driver</span></span> {<a class=sf-dump-ref>#2245</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">drivers</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:31</span> [ &#8230;31]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">guard_name</span>: \"<span class=sf-dump-str title=\"6 characters\">driver</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Driver`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Driver`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Driver`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\Driver`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1623182820\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","teams":"<pre class=sf-dump id=sf-dump-285872206 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1969</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-285872206\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","taskStatuses":"<pre class=sf-dump id=sf-dump-451741331 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>in_progress</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1602;&#1610;&#1583; &#1575;&#1604;&#1578;&#1606;&#1601;&#1610;&#1584;</span>\"
  \"<span class=sf-dump-key>advertised</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1605;&#1593;&#1604;&#1606; &#1593;&#1606;&#1607;&#1575;</span>\"
  \"<span class=sf-dump-key>assign</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1605;&#1615;&#1593;&#1610;&#1617;&#1606;&#1577;</span>\"
  \"<span class=sf-dump-key>accepted</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1605;&#1602;&#1576;&#1608;&#1604;&#1577;</span>\"
  \"<span class=sf-dump-key>started</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1576;&#1583;&#1571;&#1578;</span>\"
  \"<span class=sf-dump-key>in pickup point</span>\" => \"<span class=sf-dump-str title=\"16 characters\">&#1601;&#1610; &#1606;&#1602;&#1591;&#1577; &#1575;&#1604;&#1575;&#1587;&#1578;&#1604;&#1575;&#1605;</span>\"
  \"<span class=sf-dump-key>loading</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1580;&#1575;&#1585;&#1610; &#1575;&#1604;&#1578;&#1581;&#1605;&#1610;&#1604;</span>\"
  \"<span class=sf-dump-key>in the way</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1601;&#1610; &#1575;&#1604;&#1591;&#1585;&#1610;&#1602;</span>\"
  \"<span class=sf-dump-key>in delivery point</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1601;&#1610; &#1606;&#1602;&#1591;&#1577; &#1575;&#1604;&#1578;&#1587;&#1604;&#1610;&#1605;</span>\"
  \"<span class=sf-dump-key>unloading</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1580;&#1575;&#1585;&#1610; &#1575;&#1604;&#1578;&#1601;&#1585;&#1610;&#1594;</span>\"
  \"<span class=sf-dump-key>completed</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1605;&#1603;&#1578;&#1605;&#1604;&#1577;</span>\"
  \"<span class=sf-dump-key>canceled</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1605;&#1604;&#1594;&#1610;&#1577;</span>\"
  \"<span class=sf-dump-key>closed</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1605;&#1594;&#1604;&#1602;&#1577;</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-451741331\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","paymentStatuses":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>pending</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1601;&#1610; &#1575;&#1604;&#1575;&#1606;&#1578;&#1592;&#1575;&#1585;</span>\"
  \"<span class=sf-dump-key>paid</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1605;&#1583;&#1601;&#1608;&#1593;&#1577;</span>\"
  \"<span class=sf-dump-key>partially_paid</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1605;&#1583;&#1601;&#1608;&#1593;&#1577; &#1580;&#1586;&#1574;&#1610;&#1575;&#1611;</span>\"
  \"<span class=sf-dump-key>refunded</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1605;&#1587;&#1578;&#1585;&#1583;&#1577;</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","paymentMethods":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>cash</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1606;&#1602;&#1583;&#1575;&#1611;</span>\"
  \"<span class=sf-dump-key>bank_transfer</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1578;&#1581;&#1608;&#1610;&#1604; &#1576;&#1606;&#1603;&#1610;</span>\"
  \"<span class=sf-dump-key>credit_card</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1576;&#1591;&#1575;&#1602;&#1577; &#1575;&#1574;&#1578;&#1605;&#1575;&#1606;</span>\"
  \"<span class=sf-dump-key>wallet</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1605;&#1581;&#1601;&#1592;&#1577; &#1573;&#1604;&#1603;&#1578;&#1585;&#1608;&#1606;&#1610;&#1577;</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [admin.index] not defined. at C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('admin.index', Array, true)
#1 C:\\xampp\\htdocs\\safedestssss\\resources\\views\\admin\\reports\\customer-tasks.blade.php(36): route('admin.index')
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#5 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#7 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#9 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\EnsureUserIsAuthenticatedWithCorrectGuard.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\EnsureUserIsAuthenticatedWithCorrectGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'web')
#19 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\GlobalRateLimit.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GlobalRateLimit->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\LocaleMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [admin.index] not defined. at C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('admin.index', Array, true)
#1 C:\\xampp\\htdocs\\safedestssss\\storage\\framework\\views\\1edfcc71eede9c0724c9e8b1853ca817.php(34): route('admin.index')
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#5 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#7 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#9 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\EnsureUserIsAuthenticatedWithCorrectGuard.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\EnsureUserIsAuthenticatedWithCorrectGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'web')
#19 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\GlobalRateLimit.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GlobalRateLimit->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\LocaleMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 {main}
"} 
 
[2025-08-28 04:51:17] local.ERROR: Unable to locate file in Vite manifest: resources/js/notes.js. {"view":{"view":"C:\\xampp\\htdocs\\safedestssss\\resources\\views\\layouts\\sections\\scripts.blade.php","data":{"jsTranslations":"<pre class=sf-dump id=sf-dump-1048926941 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"24393 characters\">{&quot;Dashboards&quot;:&quot;Dashboards&quot;,&quot;Dashboard&quot;:&quot;Dashboard&quot;,&quot;eCommerce&quot;:&quot;eCommerce&quot;,&quot;CRM&quot;:&quot;CRM&quot;,&quot;Layouts&quot;:&quot;Layouts&quot;,&quot;Collapsed menu&quot;:&quot;Collapsed menu&quot;,&quot;Content navbar&quot;:&quot;Content navbar&quot;,&quot;Content nav + Sidebar&quot;:&quot;Content nav + Sidebar&quot;,&quot;Horizontal&quot;:&quot;Horizontal&quot;,&quot;Vertical&quot;:&quot;Vertical&quot;,&quot;Without menu&quot;:&quot;Without menu&quot;,&quot;Without navbar&quot;:&quot;Without navbar&quot;,&quot;Fluid&quot;:&quot;Fluid&quot;,&quot;Container&quot;:&quot;Container&quot;,&quot;Blank&quot;:&quot;Blank&quot;,&quot;Laravel Example&quot;:&quot;Laravel Example&quot;,&quot;User Management&quot;:&quot;User Management&quot;,&quot;Apps&quot;:&quot;Apps&quot;,&quot;Email&quot;:&quot;Email&quot;,&quot;Chat&quot;:&quot;Chat&quot;,&quot;Calendar&quot;:&quot;Calendar&quot;,&quot;Kanban&quot;:&quot;Kanban&quot;,&quot;Products&quot;:&quot;Products&quot;,&quot;Add Product&quot;:&quot;Add Product&quot;,&quot;Product List&quot;:&quot;Product List&quot;,&quot;Category List&quot;:&quot;Category List&quot;,&quot;Category&quot;:&quot;Category&quot;,&quot;Order&quot;:&quot;Order&quot;,&quot;Order List&quot;:&quot;Order List&quot;,&quot;Order Details&quot;:&quot;Order Details&quot;,&quot;Customer&quot;:&quot;Customer&quot;,&quot;All Customer&quot;:&quot;All Customer&quot;,&quot;All Customers&quot;:&quot;All Customers&quot;,&quot;Customer Details&quot;:&quot;Customer Details&quot;,&quot;Overview&quot;:&quot;Overview&quot;,&quot;Address &amp; Billing&quot;:&quot;Address &amp; Billing&quot;,&quot;Manage Reviews&quot;:&quot;Manage Reviews&quot;,&quot;Referrals&quot;:&quot;Referrals&quot;,&quot;Settings&quot;:&quot;Settings&quot;,&quot;Store Details&quot;:&quot;Store Details&quot;,&quot;Payments&quot;:&quot;Payments&quot;,&quot;Shipping &amp; Delivery&quot;:&quot;Shipping &amp; Delivery&quot;,&quot;Locations&quot;:&quot;Locations&quot;,&quot;Roles &amp; Permissions&quot;:&quot;Roles &amp; Permissions&quot;,&quot;Add new roles with customized permissions as per your requirement&quot;:&quot;Add new roles with customized permissions as per your requirement&quot;,&quot;Add New Role&quot;:&quot;Add New Role&quot;,&quot;Edit Role&quot;:&quot;Edit Role&quot;,&quot;Role&quot;:&quot;Role&quot;,&quot;Created At&quot;:&quot;Created At&quot;,&quot;Actions&quot;:&quot;Actions&quot;,&quot;Search User&quot;:&quot;Search User&quot;,&quot;Displaying _START_ to _END_ of _TOTAL_ entries&quot;:&quot;Displaying _START_ to _END_ of _TOTAL_ entries&quot;,&quot;Showing _START_ to _END_ of _TOTAL_ entries&quot;:&quot;Showing _START_ to _END_ of _TOTAL_ entries&quot;,&quot;Search...&quot;:&quot;Search...&quot;,&quot;add new role&quot;:&quot;add new role&quot;,&quot;role&quot;:&quot;role&quot;,&quot;role name&quot;:&quot;role name&quot;,&quot;guard&quot;:&quot;guard&quot;,&quot;Administrator&quot;:&quot;Administrator&quot;,&quot;Driver&quot;:&quot;Driver&quot;,&quot;Details of&quot;:&quot;Details of&quot;,&quot;There is no Permissions found!&quot;:&quot;There is no Permissions found!&quot;,&quot;Error!! can not fiche any Permission&quot;:&quot;Error!! can not fiche any Permission&quot;,&quot;permissions&quot;:&quot;permissions&quot;,&quot;Roles&quot;:&quot;Roles&quot;,&quot;Role Name&quot;:&quot;Role Name&quot;,&quot;Guard&quot;:&quot;Guard&quot;,&quot;Permissions&quot;:&quot;Permissions&quot;,&quot;Home&quot;:&quot;Home&quot;,&quot;Profile&quot;:&quot;Profile&quot;,&quot;Messages&quot;:&quot;Messages&quot;,&quot;Close&quot;:&quot;Close&quot;,&quot;Submit&quot;:&quot;Submit&quot;,&quot;Users&quot;:&quot;Users&quot;,&quot;Active Users&quot;:&quot;Active Users&quot;,&quot;Inactive Users&quot;:&quot;Inactive Users&quot;,&quot;Pending Users&quot;:&quot;Pending Users&quot;,&quot;Add New User&quot;:&quot;Add New User&quot;,&quot;User&quot;:&quot;User&quot;,&quot;Phone&quot;:&quot;Phone&quot;,&quot;Status&quot;:&quot;Status&quot;,&quot;Reset Password&quot;:&quot;Reset Password&quot;,&quot;Add new User&quot;:&quot;Add new User&quot;,&quot;Edit User&quot;:&quot;Edit User&quot;,&quot;Main&quot;:&quot;Main&quot;,&quot;Additional&quot;:&quot;Additional&quot;,&quot;Full Name&quot;:&quot;Full Name&quot;,&quot;<EMAIL>&quot;:&quot;<EMAIL>&quot;,&quot;Enter phone number&quot;:&quot;Enter phone number&quot;,&quot;Password&quot;:&quot;Password&quot;,&quot;Confirm Password&quot;:&quot;Confirm Password&quot;,&quot;User Role&quot;:&quot;User Role&quot;,&quot;Teams&quot;:&quot;Teams&quot;,&quot;Customers&quot;:&quot;Customers&quot;,&quot;Active Customers&quot;:&quot;Active Customers&quot;,&quot;Unverified Customers&quot;:&quot;Unverified Customers&quot;,&quot;Blocked Customers&quot;:&quot;Blocked Customers&quot;,&quot;name&quot;:&quot;name&quot;,&quot;email&quot;:&quot;email&quot;,&quot;phone&quot;:&quot;phone&quot;,&quot;tags&quot;:&quot;tags&quot;,&quot;status&quot;:&quot;status&quot;,&quot;created at&quot;:&quot;created at&quot;,&quot;actions&quot;:&quot;actions&quot;,&quot;Add New Customer&quot;:&quot;Add New Customer&quot;,&quot;Customer Role&quot;:&quot;Customer Role&quot;,&quot;Select Role&quot;:&quot;Select Role&quot;,&quot;Company Info&quot;:&quot;Company Info&quot;,&quot;Company Name&quot;:&quot;Company Name&quot;,&quot;enter company name&quot;:&quot;enter company name&quot;,&quot;Company Address&quot;:&quot;Company Address&quot;,&quot;enter company address&quot;:&quot;enter company address&quot;,&quot;Tags&quot;:&quot;Tags&quot;,&quot;Select Template&quot;:&quot;Select Template&quot;,&quot;-- Select Template&quot;:&quot;-- Select Template&quot;,&quot;--- Select Template&quot;:&quot;--- Select Template&quot;,&quot;Logistics&quot;:&quot;Logistics&quot;,&quot;Fleet&quot;:&quot;Fleet&quot;,&quot;Invoice&quot;:&quot;Invoice&quot;,&quot;Preview&quot;:&quot;Preview&quot;,&quot;Add&quot;:&quot;Add&quot;,&quot;Pages&quot;:&quot;Pages&quot;,&quot;User Profile&quot;:&quot;User Profile&quot;,&quot;Projects&quot;:&quot;Projects&quot;,&quot;Account Settings&quot;:&quot;Account Settings&quot;,&quot;Account&quot;:&quot;Account&quot;,&quot;Security&quot;:&quot;Security&quot;,&quot;Billing &amp; Plans&quot;:&quot;Billing &amp; Plans&quot;,&quot;Notifications&quot;:&quot;Notifications&quot;,&quot;Connections&quot;:&quot;Connections&quot;,&quot;FAQ&quot;:&quot;FAQ&quot;,&quot;Front Pages&quot;:&quot;Front Pages&quot;,&quot;Payment&quot;:&quot;Payment&quot;,&quot;Help Center&quot;:&quot;Help Center&quot;,&quot;Landing&quot;:&quot;Landing&quot;,&quot;Categories&quot;:&quot;Categories&quot;,&quot;Article&quot;:&quot;Article&quot;,&quot;Pricing&quot;:&quot;Pricing&quot;,&quot;Error&quot;:&quot;Error&quot;,&quot;Coming Soon&quot;:&quot;Coming Soon&quot;,&quot;Under Maintenance&quot;:&quot;Under Maintenance&quot;,&quot;Not Authorized&quot;:&quot;Not Authorized&quot;,&quot;Authentications&quot;:&quot;Authentications&quot;,&quot;Login&quot;:&quot;Login&quot;,&quot;Register&quot;:&quot;Register&quot;,&quot;Verify Email&quot;:&quot;Verify Email&quot;,&quot;Forgot Password&quot;:&quot;Forgot Password&quot;,&quot;Two Steps&quot;:&quot;Two Steps&quot;,&quot;Basic&quot;:&quot;Basic&quot;,&quot;Cover&quot;:&quot;Cover&quot;,&quot;Multi-steps&quot;:&quot;Multi-steps&quot;,&quot;Modal Examples&quot;:&quot;Modal Examples&quot;,&quot;Wizard Examples&quot;:&quot;Wizard Examples&quot;,&quot;Checkout&quot;:&quot;Checkout&quot;,&quot;Property Listing&quot;:&quot;Property Listing&quot;,&quot;Create Deal&quot;:&quot;Create Deal&quot;,&quot;Icons&quot;:&quot;Icons&quot;,&quot;Tabler&quot;:&quot;Tabler&quot;,&quot;Fontawesome&quot;:&quot;Fontawesome&quot;,&quot;User interface&quot;:&quot;User interface&quot;,&quot;Accordion&quot;:&quot;Accordion&quot;,&quot;Alerts&quot;:&quot;Alerts&quot;,&quot;App Brand&quot;:&quot;App Brand&quot;,&quot;Badges&quot;:&quot;Badges&quot;,&quot;Buttons&quot;:&quot;Buttons&quot;,&quot;Cards&quot;:&quot;Cards&quot;,&quot;Advance&quot;:&quot;Advance&quot;,&quot;Statistics&quot;:&quot;Statistics&quot;,&quot;Analytics&quot;:&quot;Analytics&quot;,&quot;Carousel&quot;:&quot;Carousel&quot;,&quot;Collapse&quot;:&quot;Collapse&quot;,&quot;Dropdowns&quot;:&quot;Dropdowns&quot;,&quot;Footer&quot;:&quot;Footer&quot;,&quot;List Groups&quot;:&quot;List Groups&quot;,&quot;Modals&quot;:&quot;Modals&quot;,&quot;Menu&quot;:&quot;Menu&quot;,&quot;Navbar&quot;:&quot;Navbar&quot;,&quot;Offcanvas&quot;:&quot;Offcanvas&quot;,&quot;Pagination &amp; Breadcrumbs&quot;:&quot;Pagination &amp; Breadcrumbs&quot;,&quot;Progress&quot;:&quot;Progress&quot;,&quot;Spinners&quot;:&quot;Spinners&quot;,&quot;Tabs &amp; Pills&quot;:&quot;Tabs &amp; Pills&quot;,&quot;Toasts&quot;:&quot;Toasts&quot;,&quot;Tooltips &amp; Popovers&quot;:&quot;Tooltips &amp; Popovers&quot;,&quot;Typography&quot;:&quot;Typography&quot;,&quot;Extended UI&quot;:&quot;Extended UI&quot;,&quot;Avatar&quot;:&quot;Avatar&quot;,&quot;BlockUI&quot;:&quot;BlockUI&quot;,&quot;Drag &amp; Drop&quot;:&quot;Drag &amp; Drop&quot;,&quot;Media Player&quot;:&quot;Media Player&quot;,&quot;Perfect Scrollbar&quot;:&quot;Perfect Scrollbar&quot;,&quot;Star Ratings&quot;:&quot;Star Ratings&quot;,&quot;SweetAlert2&quot;:&quot;SweetAlert2&quot;,&quot;Text Divider&quot;:&quot;Text Divider&quot;,&quot;Timeline&quot;:&quot;Timeline&quot;,&quot;Fullscreen&quot;:&quot;Fullscreen&quot;,&quot;Tour&quot;:&quot;Tour&quot;,&quot;Treeview&quot;:&quot;Treeview&quot;,&quot;Miscellaneous&quot;:&quot;Miscellaneous&quot;,&quot;Misc&quot;:&quot;Misc&quot;,&quot;Form Elements&quot;:&quot;Form Elements&quot;,&quot;Basic Inputs&quot;:&quot;Basic Inputs&quot;,&quot;Input groups&quot;:&quot;Input groups&quot;,&quot;Custom Options&quot;:&quot;Custom Options&quot;,&quot;Editors&quot;:&quot;Editors&quot;,&quot;File Upload&quot;:&quot;File Upload&quot;,&quot;Pickers&quot;:&quot;Pickers&quot;,&quot;Select &amp; Tags&quot;:&quot;Select &amp; Tags&quot;,&quot;Sliders&quot;:&quot;Sliders&quot;,&quot;Switches&quot;:&quot;Switches&quot;,&quot;Extras&quot;:&quot;Extras&quot;,&quot;Form Layouts&quot;:&quot;Form Layouts&quot;,&quot;Vertical Form&quot;:&quot;Vertical Form&quot;,&quot;Horizontal Form&quot;:&quot;Horizontal Form&quot;,&quot;Sticky Actions&quot;:&quot;Sticky Actions&quot;,&quot;Form Wizard&quot;:&quot;Form Wizard&quot;,&quot;Numbered&quot;:&quot;Numbered&quot;,&quot;Advanced&quot;:&quot;Advanced&quot;,&quot;Forms&quot;:&quot;Forms&quot;,&quot;Form Validation&quot;:&quot;Form Validation&quot;,&quot;Tables&quot;:&quot;Tables&quot;,&quot;Datatables&quot;:&quot;Datatables&quot;,&quot;Extensions&quot;:&quot;Extensions&quot;,&quot;Charts&quot;:&quot;Charts&quot;,&quot;Apex Charts&quot;:&quot;Apex Charts&quot;,&quot;ChartJS&quot;:&quot;ChartJS&quot;,&quot;Leaflet Maps&quot;:&quot;Leaflet Maps&quot;,&quot;Support&quot;:&quot;Support&quot;,&quot;Documentation&quot;:&quot;Documentation&quot;,&quot;Academy&quot;:&quot;Academy&quot;,&quot;My Course&quot;:&quot;My Course&quot;,&quot;Course Details&quot;:&quot;Course Details&quot;,&quot;Apps &amp; Pages&quot;:&quot;Apps &amp; Pages&quot;,&quot;Components&quot;:&quot;Components&quot;,&quot;Forms &amp; Tables&quot;:&quot;Forms &amp; Tables&quot;,&quot;Charts &amp; Maps&quot;:&quot;Charts &amp; Maps&quot;,&quot;Id&quot;:&quot;Id&quot;,&quot;General&quot;:&quot;General&quot;,&quot;Template&quot;:&quot;Template&quot;,&quot;Donut drag\\u00e9e jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.&quot;:&quot;Donut drag\\u00e9e jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.&quot;,&quot;Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.&quot;:&quot;Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.&quot;,&quot;Geo-fence&quot;:&quot;Geo-fence&quot;,&quot;It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.&quot;:&quot;It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.&quot;,&quot;Add New Geo-fence&quot;:&quot;Add New Geo-fence&quot;,&quot;\\ud83d\\udd0d Search Team&quot;:&quot;\\ud83d\\udd0d Search Team&quot;,&quot;Geofences&quot;:&quot;Geofences&quot;,&quot;Add Geo-fence&quot;:&quot;Add Geo-fence&quot;,&quot;Select Teams&quot;:&quot;Select Teams&quot;,&quot;Name&quot;:&quot;Name&quot;,&quot;Enter name&quot;:&quot;Enter name&quot;,&quot;Description&quot;:&quot;Description&quot;,&quot;Enter description&quot;:&quot;Enter description&quot;,&quot;The role name is required.&quot;:&quot;The role name is required.&quot;,&quot;The role name has already been taken.&quot;:&quot;The role name has already been taken.&quot;,&quot;The guard field is required.&quot;:&quot;The guard field is required.&quot;,&quot;The selected guard is invalid.&quot;:&quot;The selected guard is invalid.&quot;,&quot;At least one permission must be selected.&quot;:&quot;At least one permission must be selected.&quot;,&quot;Permissions must be an array.&quot;:&quot;Permissions must be an array.&quot;,&quot;Error to Save Role&quot;:&quot;Error to Save Role&quot;,&quot;Role Saved&quot;:&quot;Role Saved&quot;,&quot;This role can not be deleted&quot;:&quot;This role can not be deleted&quot;,&quot;There are users connected with this role&quot;:&quot;There are users connected with this role&quot;,&quot;Error to delete role&quot;:&quot;Error to delete role&quot;,&quot;Role deleted&quot;:&quot;Role deleted&quot;,&quot;Rate&quot;:&quot;Rate&quot;,&quot;Fixed&quot;:&quot;Fixed&quot;,&quot;Commission Rate&quot;:&quot;Commission Rate&quot;,&quot;Commission fixed Amount&quot;:&quot;Commission fixed Amount&quot;,&quot;General Settings&quot;:&quot;General Settings&quot;,&quot;You can manage the main and vital settings of the platform from here, so be careful.&quot;:&quot;You can manage the main and vital settings of the platform from here, so be careful.&quot;,&quot;Templates&quot;:&quot;Templates&quot;,&quot;Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more&quot;:&quot;Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more&quot;,&quot;Add New Template&quot;:&quot;Add New Template&quot;,&quot;Template Name&quot;:&quot;Template Name&quot;,&quot;enter the Template name&quot;:&quot;enter the Template name&quot;,&quot;Default Customer Template&quot;:&quot;Default Customer Template&quot;,&quot;Default Driver Template&quot;:&quot;Default Driver Template&quot;,&quot;Default User Template&quot;:&quot;Default User Template&quot;,&quot;Default Task Template&quot;:&quot;Default Task Template&quot;,&quot;Drivers Commission&quot;:&quot;Drivers Commission&quot;,&quot;Commission Type&quot;:&quot;Commission Type&quot;,&quot;The user id is required.&quot;:&quot;The user id is required.&quot;,&quot;The selected user does not exist.&quot;:&quot;The selected user does not exist.&quot;,&quot;The status field is required.&quot;:&quot;The status field is required.&quot;,&quot;Error to Change user Status&quot;:&quot;Error to Change user Status&quot;,&quot;User Status changed&quot;:&quot;User Status changed&quot;,&quot;The name field is required.&quot;:&quot;The name field is required.&quot;,&quot;The email field is required.&quot;:&quot;The email field is required.&quot;,&quot;The email has already been taken.&quot;:&quot;The email has already been taken.&quot;,&quot;The phone field is required.&quot;:&quot;The phone field is required.&quot;,&quot;The phone has already been taken.&quot;:&quot;The phone has already been taken.&quot;,&quot;The password field is required.&quot;:&quot;The password field is required.&quot;,&quot;The password and confirmation must match.&quot;:&quot;The password and confirmation must match.&quot;,&quot;The user role is required.&quot;:&quot;The user role is required.&quot;,&quot;The selected role is invalid.&quot;:&quot;The selected role is invalid.&quot;,&quot;Teams must be an array.&quot;:&quot;Teams must be an array.&quot;,&quot;Customers must be an array.&quot;:&quot;Customers must be an array.&quot;,&quot;The selected template is invalid.&quot;:&quot;The selected template is invalid.&quot;,&quot;The :label field is required.&quot;:&quot;The :label field is required.&quot;,&quot;Can not find the selected user&quot;:&quot;Can not find the selected user&quot;,&quot;User saved successfully&quot;:&quot;User saved successfully&quot;,&quot;User not found&quot;:&quot;User not found&quot;,&quot;Error to change reset password  status&quot;:&quot;Error to change reset password  status&quot;,&quot;User deleted&quot;:&quot;User deleted&quot;,&quot;The selected User has teams to mange. you can not delete hem right now&quot;:&quot;The selected User has teams to mange. you can not delete hem right now&quot;,&quot;Error to delete User&quot;:&quot;Error to delete User&quot;,&quot;Vehicles&quot;:&quot;Vehicles&quot;,&quot;Managing the types of vehicles and trucks that will provide delivery services on the platform&quot;:&quot;Managing the types of vehicles and trucks that will provide delivery services on the platform&quot;,&quot;Vehicles Types&quot;:&quot;Vehicles Types&quot;,&quot;Vehicles Sizes&quot;:&quot;Vehicles Sizes&quot;,&quot;vehicle name&quot;:&quot;vehicle name&quot;,&quot;English name&quot;:&quot;English name&quot;,&quot;save&quot;:&quot;save&quot;,&quot;types&quot;:&quot;types&quot;,&quot;Select vehicle&quot;:&quot;Select vehicle&quot;,&quot;select vehicle&quot;:&quot;select vehicle&quot;,&quot;Flitter by vehicle&quot;:&quot;Flitter by vehicle&quot;,&quot;all vehicle&quot;:&quot;all vehicle&quot;,&quot;vehicle&quot;:&quot;vehicle&quot;,&quot;type name&quot;:&quot;type name&quot;,&quot;sizes&quot;:&quot;sizes&quot;,&quot;size&quot;:&quot;size&quot;,&quot;Select vehicle Type&quot;:&quot;Select vehicle Type&quot;,&quot;Flitter by vehicle type&quot;:&quot;Flitter by vehicle type&quot;,&quot;select vehicle type&quot;:&quot;select vehicle type&quot;,&quot;vehicle type&quot;:&quot;vehicle type&quot;,&quot;No data available&quot;:&quot;No data available&quot;,&quot;select vehicle Size&quot;:&quot;select vehicle Size&quot;,&quot;Add New Tag&quot;:&quot;Add New Tag&quot;,&quot;tag name&quot;:&quot;tag name&quot;,&quot;enter the tag name&quot;:&quot;enter the tag name&quot;,&quot;tag slug&quot;:&quot;tag slug&quot;,&quot;enter the tag slug&quot;:&quot;enter the tag slug&quot;,&quot;Select Tags&quot;:&quot;Select Tags&quot;,&quot;The tag name is required.&quot;:&quot;The tag name is required.&quot;,&quot;The tag name has already been taken.&quot;:&quot;The tag name has already been taken.&quot;,&quot;The tag slug is required.&quot;:&quot;The tag slug is required.&quot;,&quot;The tag slug has already been taken.&quot;:&quot;The tag slug has already been taken.&quot;,&quot;The description must be a string.&quot;:&quot;The description must be a string.&quot;,&quot;The description may not be greater than 400 characters.&quot;:&quot;The description may not be greater than 400 characters.&quot;,&quot;Can not find the selected Tag&quot;:&quot;Can not find the selected Tag&quot;,&quot;Error: can not save the Tag&quot;:&quot;Error: can not save the Tag&quot;,&quot;Tag saved successfully&quot;:&quot;Tag saved successfully&quot;,&quot;Error to find selected Tag&quot;:&quot;Error to find selected Tag&quot;,&quot;Error to delete Tag&quot;:&quot;Error to delete Tag&quot;,&quot;Tag deleted&quot;:&quot;Tag deleted&quot;,&quot;The geofence name is required.&quot;:&quot;The geofence name is required.&quot;,&quot;The geofence name has already been taken.&quot;:&quot;The geofence name has already been taken.&quot;,&quot;The coordinates field is required.&quot;:&quot;The coordinates field is required.&quot;,&quot;The coordinates must be a string.&quot;:&quot;The coordinates must be a string.&quot;,&quot;Can not find the selected Geo-Fence&quot;:&quot;Can not find the selected Geo-Fence&quot;,&quot;error to save Geo-Fence&quot;:&quot;error to save Geo-Fence&quot;,&quot;Geo-Fence saved successfully&quot;:&quot;Geo-Fence saved successfully&quot;,&quot;Error to delete Geo-fence&quot;:&quot;Error to delete Geo-fence&quot;,&quot;Geo-fence deleted&quot;:&quot;Geo-fence deleted&quot;,&quot;Points&quot;:&quot;Points&quot;,&quot;Add New Point&quot;:&quot;Add New Point&quot;,&quot;address&quot;:&quot;address&quot;,&quot;customer&quot;:&quot;customer&quot;,&quot;enter the point name&quot;:&quot;enter the point name&quot;,&quot;enter the point address&quot;:&quot;enter the point address&quot;,&quot;Location&quot;:&quot;Location&quot;,&quot;confirm location&quot;:&quot;confirm location&quot;,&quot;Contact name&quot;:&quot;Contact name&quot;,&quot;enter the point contact name&quot;:&quot;enter the point contact name&quot;,&quot;Contact phone&quot;:&quot;Contact phone&quot;,&quot;enter the point contact phone&quot;:&quot;enter the point contact phone&quot;,&quot;Select Customer&quot;:&quot;Select Customer&quot;,&quot;The point name is required.&quot;:&quot;The point name is required.&quot;,&quot;The point name must be a string.&quot;:&quot;The point name must be a string.&quot;,&quot;The contact name must be a string.&quot;:&quot;The contact name must be a string.&quot;,&quot;The contact name may not be greater than 400 characters.&quot;:&quot;The contact name may not be greater than 400 characters.&quot;,&quot;The contact phone must be a string.&quot;:&quot;The contact phone must be a string.&quot;,&quot;The contact phone may not be greater than 50 characters.&quot;:&quot;The contact phone may not be greater than 50 characters.&quot;,&quot;The address field is required.&quot;:&quot;The address field is required.&quot;,&quot;The address must be a string.&quot;:&quot;The address must be a string.&quot;,&quot;The address may not be greater than 500 characters.&quot;:&quot;The address may not be greater than 500 characters.&quot;,&quot;The latitude field is required.&quot;:&quot;The latitude field is required.&quot;,&quot;The latitude must be a number.&quot;:&quot;The latitude must be a number.&quot;,&quot;The longitude field is required.&quot;:&quot;The longitude field is required.&quot;,&quot;The longitude must be a number.&quot;:&quot;The longitude must be a number.&quot;,&quot;The selected customer is invalid.&quot;:&quot;The selected customer is invalid.&quot;,&quot;Can not find the selected Point&quot;:&quot;Can not find the selected Point&quot;,&quot;Error: can not save the Point&quot;:&quot;Error: can not save the Point&quot;,&quot;Point saved successfully&quot;:&quot;Point saved successfully&quot;,&quot;Error to delete Point. its connect with pricing mater&quot;:&quot;Error to delete Point. its connect with pricing mater&quot;,&quot;Error to delete Point&quot;:&quot;Error to delete Point&quot;,&quot;Point deleted&quot;:&quot;Point deleted&quot;,&quot;Blockages&quot;:&quot;Blockages&quot;,&quot;Add a new Blockage&quot;:&quot;Add a new Blockage&quot;,&quot;type&quot;:&quot;type&quot;,&quot;description&quot;:&quot;description&quot;,&quot;coordinates&quot;:&quot;coordinates&quot;,&quot;Block Type&quot;:&quot;Block Type&quot;,&quot;Select Type&quot;:&quot;Select Type&quot;,&quot;Point Closed&quot;:&quot;Point Closed&quot;,&quot;Line Closed&quot;:&quot;Line Closed&quot;,&quot;Block Description&quot;:&quot;Block Description&quot;,&quot;optional&quot;:&quot;optional&quot;,&quot;draw the Points on the map&quot;:&quot;draw the Points on the map&quot;,&quot;Save&quot;:&quot;Save&quot;,&quot;Cancel&quot;:&quot;Cancel&quot;,&quot;The blockage type is required.&quot;:&quot;The blockage type is required.&quot;,&quot;The selected blockage type is invalid.&quot;:&quot;The selected blockage type is invalid.&quot;,&quot;Error: can not save the Blockage&quot;:&quot;Error: can not save the Blockage&quot;,&quot;Blockage saved successfully&quot;:&quot;Blockage saved successfully&quot;,&quot;Error to delete Blockage&quot;:&quot;Error to delete Blockage&quot;,&quot;Blockage deleted&quot;:&quot;Blockage deleted&quot;,&quot;Pricing Methods&quot;:&quot;Pricing Methods&quot;,&quot;Method&quot;:&quot;Method&quot;,&quot;Add New Method&quot;:&quot;Add New Method&quot;,&quot;Method Name&quot;:&quot;Method Name&quot;,&quot;enter the Method name&quot;:&quot;enter the Method name&quot;,&quot;Edit Method&quot;:&quot;Edit Method&quot;,&quot;The method name is required.&quot;:&quot;The method name is required.&quot;,&quot;The method name has already been taken.&quot;:&quot;The method name has already been taken.&quot;,&quot;The description field is required.&quot;:&quot;The description field is required.&quot;,&quot;Can not find the selected Pricing Method&quot;:&quot;Can not find the selected Pricing Method&quot;,&quot;error to save Pricing Method&quot;:&quot;error to save Pricing Method&quot;,&quot;Pricing Method saved successfully&quot;:&quot;Pricing Method saved successfully&quot;,&quot;Pricing Method not found&quot;:&quot;Pricing Method not found&quot;,&quot;Error to change Pricing Method status&quot;:&quot;Error to change Pricing Method status&quot;,&quot;label&quot;:&quot;label&quot;,&quot;driver can&quot;:&quot;driver can&quot;,&quot;customer can&quot;:&quot;customer can&quot;,&quot;value&quot;:&quot;value&quot;,&quot;require&quot;:&quot;require&quot;,&quot;Select Values&quot;:&quot;Select Values&quot;,&quot;Value&quot;:&quot;Value&quot;,&quot;Display Name&quot;:&quot;Display Name&quot;,&quot;Action&quot;:&quot;Action&quot;,&quot;More&quot;:&quot;More&quot;,&quot;Customers Selections&quot;:&quot;Customers Selections&quot;,&quot;Apply to All Customers&quot;:&quot;Apply to All Customers&quot;,&quot;Use customers tags&quot;:&quot;Use customers tags&quot;,&quot;Use Specific customers&quot;:&quot;Use Specific customers&quot;,&quot;Vehicles Selections&quot;:&quot;Vehicles Selections&quot;,&quot;Base Fare&quot;:&quot;Base Fare&quot;,&quot;Base Distance&quot;:&quot;Base Distance&quot;,&quot;Base Waiting&quot;:&quot;Base Waiting&quot;,&quot;Distance Fare&quot;:&quot;Distance Fare&quot;,&quot;Waiting Fare&quot;:&quot;Waiting Fare&quot;,&quot;Dynamic Pricing Based on Field Values&quot;:&quot;Dynamic Pricing Based on Field Values&quot;,&quot;add field&quot;:&quot;add field&quot;,&quot;Dynamic Pricing Based on Geo-fence&quot;:&quot;Dynamic Pricing Based on Geo-fence&quot;,&quot;add geofence&quot;:&quot;add geofence&quot;,&quot;Commission&quot;:&quot;Commission&quot;,&quot;VAT Commission&quot;:&quot;VAT Commission&quot;,&quot;Service Tax Commission&quot;:&quot;Service Tax Commission&quot;,&quot;Discount Fare&quot;:&quot;Discount Fare&quot;,&quot;Discount percentage %&quot;:&quot;Discount percentage %&quot;,&quot;The rule name is required.&quot;:&quot;The rule name is required.&quot;,&quot;The rule name must be a string.&quot;:&quot;The rule name must be a string.&quot;,&quot;The rule name may not be greater than 255 characters.&quot;:&quot;The rule name may not be greater than 255 characters.&quot;,&quot;The rule name has already been taken.&quot;:&quot;The rule name has already been taken.&quot;,&quot;The decimal places field is required.&quot;:&quot;The decimal places field is required.&quot;,&quot;The decimal places must be an integer.&quot;:&quot;The decimal places must be an integer.&quot;,&quot;The decimal places must be at least 0.&quot;:&quot;The decimal places must be at least 0.&quot;,&quot;The decimal places may not be greater than 10.&quot;:&quot;The decimal places may not be greater than 10.&quot;,&quot;The form template is required.&quot;:&quot;The form template is required.&quot;,&quot;The form template id must be an integer.&quot;:&quot;The form template id must be an integer.&quot;,&quot;The selected form template is invalid.&quot;:&quot;The selected form template is invalid.&quot;,&quot;At least one customer must be selected.&quot;:&quot;At least one customer must be selected.&quot;,&quot;Each customer is required.&quot;:&quot;Each customer is required.&quot;,&quot;Each customer id must be an integer.&quot;:&quot;Each customer id must be an integer.&quot;,&quot;At least one tag must be selected.&quot;:&quot;At least one tag must be selected.&quot;,&quot;Tags must be an array.&quot;:&quot;Tags must be an array.&quot;,&quot;Each tag is required.&quot;:&quot;Each tag is required.&quot;,&quot;Each tag id must be an integer.&quot;:&quot;Each tag id must be an integer.&quot;,&quot;The selected tag is invalid.&quot;:&quot;The selected tag is invalid.&quot;,&quot;At least one vehicle size must be selected.&quot;:&quot;At least one vehicle size must be selected.&quot;,&quot;Sizes must be an array.&quot;:&quot;Sizes must be an array.&quot;,&quot;Each size id must be an integer.&quot;:&quot;Each size id must be an integer.&quot;,&quot;The selected vehicle size is invalid.&quot;:&quot;The selected vehicle size is invalid.&quot;,&quot;The base fare field is required.&quot;:&quot;The base fare field is required.&quot;,&quot;The base fare must be a number.&quot;:&quot;The base fare must be a number.&quot;,&quot;The base fare must be at least 0.&quot;:&quot;The base fare must be at least 0.&quot;,&quot;The base distance field is required.&quot;:&quot;The base distance field is required.&quot;,&quot;The base distance must be a number.&quot;:&quot;The base distance must be a number.&quot;,&quot;The base distance must be at least 0.&quot;:&quot;The base distance must be at least 0.&quot;,&quot;The base waiting field is required.&quot;:&quot;The base waiting field is required.&quot;,&quot;The base waiting must be a number.&quot;:&quot;The base waiting must be a number.&quot;,&quot;The base waiting must be at least 0.&quot;:&quot;The base waiting must be at least 0.&quot;,&quot;The distance fare field is required.&quot;:&quot;The distance fare field is required.&quot;,&quot;The distance fare must be a number.&quot;:&quot;The distance fare must be a number.&quot;,&quot;The distance fare must be at least 0.&quot;:&quot;The distance fare must be at least 0.&quot;,&quot;The waiting fare field is required.&quot;:&quot;The waiting fare field is required.&quot;,&quot;The waiting fare must be a number.&quot;:&quot;The waiting fare must be a number.&quot;,&quot;The waiting fare must be at least 0.&quot;:&quot;The waiting fare must be at least 0.&quot;,&quot;The VAT commission field is required.&quot;:&quot;The VAT commission field is required.&quot;,&quot;The VAT commission must be a number.&quot;:&quot;The VAT commission must be a number.&quot;,&quot;The VAT commission must be at least 0.&quot;:&quot;The VAT commission must be at least 0.&quot;,&quot;The VAT commission may not be greater than 100.&quot;:&quot;The VAT commission may not be greater than 100.&quot;,&quot;The service commission field is required.&quot;:&quot;The service commission field is required.&quot;,&quot;The service commission must be a number.&quot;:&quot;The service commission must be a number.&quot;,&quot;The service commission must be at least 0.&quot;:&quot;The service commission must be at least 0.&quot;,&quot;The service commission may not be greater than 100.&quot;:&quot;The service commission may not be greater than 100.&quot;,&quot;The discount must be a number.&quot;:&quot;The discount must be a number.&quot;,&quot;The discount must be at least 0.&quot;:&quot;The discount must be at least 0.&quot;,&quot;The discount may not be greater than 100.&quot;:&quot;The discount may not be greater than 100.&quot;,&quot;Task Done&quot;:&quot;Task Done&quot;,&quot;Running Tasks&quot;:&quot;Running Tasks&quot;,&quot;Details&quot;:&quot;Details&quot;,&quot;Edit&quot;:&quot;Edit&quot;,&quot;Suspend&quot;:&quot;Suspend&quot;,&quot;Tasks&quot;:&quot;Tasks&quot;,&quot;Total&quot;:&quot;Total&quot;,&quot;Issued Date&quot;:&quot;Issued Date&quot;,&quot;Additional Fields&quot;:&quot;Additional Fields&quot;,&quot;No additional data found for this customer.&quot;:&quot;No additional data found for this customer.&quot;,&quot;View&quot;:&quot;View&quot;,&quot;Change Status&quot;:&quot;Change Status&quot;,&quot;Create Wallet&quot;:&quot;Create Wallet&quot;,&quot;The customer id is required.&quot;:&quot;The customer id is required.&quot;,&quot;The selected customer does not exist.&quot;:&quot;The selected customer does not exist.&quot;,&quot;You do not have permission to do actions to this record&quot;:&quot;You do not have permission to do actions to this record&quot;,&quot;Error to Change Customer Status&quot;:&quot;Error to Change Customer Status&quot;,&quot;Customer Status changed&quot;:&quot;Customer Status changed&quot;,&quot;Can not find the selected Customer&quot;:&quot;Can not find the selected Customer&quot;,&quot;Error: can not save the Customer&quot;:&quot;Error: can not save the Customer&quot;,&quot;Customer saved successfully&quot;:&quot;Customer saved successfully&quot;,&quot;Error to delete Customer&quot;:&quot;Error to delete Customer&quot;,&quot;Customer deleted&quot;:&quot;Customer deleted&quot;,&quot;The driver id is required.&quot;:&quot;The driver id is required.&quot;,&quot;The selected driver does not exist.&quot;:&quot;The selected driver does not exist.&quot;,&quot;Error to Change Driver Status&quot;:&quot;Error to Change Driver Status&quot;,&quot;Driver Status changed&quot;:&quot;Driver Status changed&quot;,&quot;Can not find the selected Driver&quot;:&quot;Can not find the selected Driver&quot;,&quot;Error: can not save the Driver&quot;:&quot;Error: can not save the Driver&quot;,&quot;Driver saved successfully&quot;:&quot;Driver saved successfully&quot;,&quot;Error to delete Driver&quot;:&quot;Error to delete Driver&quot;,&quot;Driver deleted&quot;:&quot;Driver deleted&quot;,&quot;Drivers&quot;:&quot;Drivers&quot;,&quot;Active Drivers&quot;:&quot;Active Drivers&quot;,&quot;Pending Drivers&quot;:&quot;Pending Drivers&quot;,&quot;Blocked Drivers&quot;:&quot;Blocked Drivers&quot;,&quot;Unverified Drivers&quot;:&quot;Unverified Drivers&quot;,&quot;username&quot;:&quot;username&quot;,&quot;Add new Driver&quot;:&quot;Add new Driver&quot;,&quot;Username&quot;:&quot;Username&quot;,&quot;Team&quot;:&quot;Team&quot;,&quot;Select Team&quot;:&quot;Select Team&quot;,&quot;Driver Role&quot;:&quot;Driver Role&quot;,&quot;Home Address&quot;:&quot;Home Address&quot;,&quot;enter home address&quot;:&quot;enter home address&quot;,&quot;Select Commission Type&quot;:&quot;Select Commission Type&quot;,&quot;\\u064cRate&quot;:&quot;Rate&quot;,&quot;Fixed Amount&quot;:&quot;Fixed Amount&quot;,&quot;Subscription Monthly&quot;:&quot;Subscription Monthly&quot;,&quot;Commission Amount&quot;:&quot;Commission Amount&quot;,&quot;Vehicle Selection&quot;:&quot;Vehicle Selection&quot;,&quot;Wallets&quot;:&quot;Wallets&quot;,&quot;balance&quot;:&quot;balance&quot;,&quot;Debt Ceiling&quot;:&quot;Debt Ceiling&quot;,&quot;preview&quot;:&quot;preview&quot;,&quot;last transaction&quot;:&quot;last transaction&quot;,&quot;Amount&quot;:&quot;Amount&quot;,&quot;Maturity&quot;:&quot;Maturity&quot;,&quot;Task&quot;:&quot;Task&quot;,&quot;Add New Transaction&quot;:&quot;Add New Transaction&quot;,&quot;Enter the amount&quot;:&quot;Enter the amount&quot;,&quot;Transaction Type&quot;:&quot;Transaction Type&quot;,&quot;Credit&quot;:&quot;Credit&quot;,&quot;Debit&quot;:&quot;Debit&quot;,&quot;Maturity Time&quot;:&quot;Maturity Time&quot;,&quot;Optional notes...&quot;:&quot;Optional notes...&quot;,&quot;Image &quot;:&quot;Image &quot;,&quot;View the image&quot;:&quot;View the image&quot;,&quot;close&quot;:&quot;close&quot;,&quot;image&quot;:&quot;image&quot;}</span>\"
</pre><script>Sfdump(\"sf-dump-1048926941\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-1058494912 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1012</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1058494912\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","configData":"<pre class=sf-dump id=sf-dump-1833645275 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>layout</span>\" => \"<span class=sf-dump-str title=\"8 characters\">vertical</span>\"
  \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"13 characters\">theme-default</span>\"
  \"<span class=sf-dump-key>themeOpt</span>\" => \"<span class=sf-dump-str title=\"13 characters\">theme-default</span>\"
  \"<span class=sf-dump-key>style</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"
  \"<span class=sf-dump-key>styleOpt</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"
  \"<span class=sf-dump-key>styleOptVal</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"
  \"<span class=sf-dump-key>rtlSupport</span>\" => \"<span class=sf-dump-str title=\"4 characters\">/rtl</span>\"
  \"<span class=sf-dump-key>rtlMode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"
  \"<span class=sf-dump-key>textDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"
  \"<span class=sf-dump-key>menuCollapsed</span>\" => \"<span class=sf-dump-str title=\"21 characters\">layout-menu-collapsed</span>\"
  \"<span class=sf-dump-key>hasCustomizer</span>\" => <span class=sf-dump-const>true</span>
  \"<span class=sf-dump-key>showDropdownOnHover</span>\" => <span class=sf-dump-const>true</span>
  \"<span class=sf-dump-key>displayCustomizer</span>\" => <span class=sf-dump-const>false</span>
  \"<span class=sf-dump-key>contentLayout</span>\" => \"<span class=sf-dump-str title=\"4 characters\">wide</span>\"
  \"<span class=sf-dump-key>headerType</span>\" => \"<span class=sf-dump-str title=\"17 characters\">layout-menu-fixed</span>\"
  \"<span class=sf-dump-key>navbarType</span>\" => \"<span class=sf-dump-str title=\"19 characters\">layout-navbar-fixed</span>\"
  \"<span class=sf-dump-key>menuFixed</span>\" => \"<span class=sf-dump-str title=\"17 characters\">layout-menu-fixed</span>\"
  \"<span class=sf-dump-key>footerFixed</span>\" => <span class=sf-dump-const>false</span>
  \"<span class=sf-dump-key>customizerControls</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">rtl</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">headerType</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"13 characters\">contentLayout</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"15 characters\">layoutCollapsed</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"19 characters\">layoutNavbarOptions</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"6 characters\">themes</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1833645275\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","customizerHidden":"<pre class=sf-dump id=sf-dump-299968372 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"15 characters\">customizer-hide</span>\"
</pre><script>Sfdump(\"sf-dump-299968372\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","menuFixed":"<pre class=sf-dump id=sf-dump-2015659686 data-indent-pad=\"  \">\"\"
</pre><script>Sfdump(\"sf-dump-2015659686\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","navbarType":"<pre class=sf-dump id=sf-dump-1093799820 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"19 characters\">layout-navbar-fixed</span>\"
</pre><script>Sfdump(\"sf-dump-1093799820\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","isFront":"<pre class=sf-dump id=sf-dump-774417888 data-indent-pad=\"  \">\"\"
</pre><script>Sfdump(\"sf-dump-774417888\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","contentLayout":"<pre class=sf-dump id=sf-dump-1596841785 data-indent-pad=\"  \">\"\"
</pre><script>Sfdump(\"sf-dump-1596841785\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","locale":"<pre class=sf-dump id=sf-dump-1505171985 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">en</span>\"
</pre><script>Sfdump(\"sf-dump-1505171985\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","langFile":"<pre class=sf-dump id=sf-dump-288165374 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"41 characters\">C:\\xampp\\htdocs\\safedestssss\\lang/en.json</span>\"
</pre><script>Sfdump(\"sf-dump-288165374\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","translationsA":"<pre class=sf-dump id=sf-dump-1838441746 data-indent-pad=\"  \"><span class=sf-dump-note>array:570</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>Dashboards</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Dashboards</span>\"
  \"<span class=sf-dump-key>Dashboard</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Dashboard</span>\"
  \"<span class=sf-dump-key>eCommerce</span>\" => \"<span class=sf-dump-str title=\"9 characters\">eCommerce</span>\"
  \"<span class=sf-dump-key>CRM</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CRM</span>\"
  \"<span class=sf-dump-key>Layouts</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Layouts</span>\"
  \"<span class=sf-dump-key>Collapsed menu</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Collapsed menu</span>\"
  \"<span class=sf-dump-key>Content navbar</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Content navbar</span>\"
  \"<span class=sf-dump-key>Content nav + Sidebar</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Content nav + Sidebar</span>\"
  \"<span class=sf-dump-key>Horizontal</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Horizontal</span>\"
  \"<span class=sf-dump-key>Vertical</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Vertical</span>\"
  \"<span class=sf-dump-key>Without menu</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Without menu</span>\"
  \"<span class=sf-dump-key>Without navbar</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Without navbar</span>\"
  \"<span class=sf-dump-key>Fluid</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Fluid</span>\"
  \"<span class=sf-dump-key>Container</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Container</span>\"
  \"<span class=sf-dump-key>Blank</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Blank</span>\"
  \"<span class=sf-dump-key>Laravel Example</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Laravel Example</span>\"
  \"<span class=sf-dump-key>User Management</span>\" => \"<span class=sf-dump-str title=\"15 characters\">User Management</span>\"
  \"<span class=sf-dump-key>Apps</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Apps</span>\"
  \"<span class=sf-dump-key>Email</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"
  \"<span class=sf-dump-key>Chat</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Chat</span>\"
  \"<span class=sf-dump-key>Calendar</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Calendar</span>\"
  \"<span class=sf-dump-key>Kanban</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Kanban</span>\"
  \"<span class=sf-dump-key>Products</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"
  \"<span class=sf-dump-key>Add Product</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Add Product</span>\"
  \"<span class=sf-dump-key>Product List</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product List</span>\"
  \"<span class=sf-dump-key>Category List</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Category List</span>\"
  \"<span class=sf-dump-key>Category</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"
  \"<span class=sf-dump-key>Order</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Order</span>\"
  \"<span class=sf-dump-key>Order List</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Order List</span>\"
  \"<span class=sf-dump-key>Order Details</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Order Details</span>\"
  \"<span class=sf-dump-key>Customer</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Customer</span>\"
  \"<span class=sf-dump-key>All Customer</span>\" => \"<span class=sf-dump-str title=\"12 characters\">All Customer</span>\"
  \"<span class=sf-dump-key>All Customers</span>\" => \"<span class=sf-dump-str title=\"13 characters\">All Customers</span>\"
  \"<span class=sf-dump-key>Customer Details</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Customer Details</span>\"
  \"<span class=sf-dump-key>Overview</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Overview</span>\"
  \"<span class=sf-dump-key>Address &amp; Billing</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Address &amp; Billing</span>\"
  \"<span class=sf-dump-key>Manage Reviews</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Manage Reviews</span>\"
  \"<span class=sf-dump-key>Referrals</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Referrals</span>\"
  \"<span class=sf-dump-key>Settings</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Settings</span>\"
  \"<span class=sf-dump-key>Store Details</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Store Details</span>\"
  \"<span class=sf-dump-key>Payments</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Payments</span>\"
  \"<span class=sf-dump-key>Shipping &amp; Delivery</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Shipping &amp; Delivery</span>\"
  \"<span class=sf-dump-key>Locations</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Locations</span>\"
  \"<span class=sf-dump-key>Roles &amp; Permissions</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Roles &amp; Permissions</span>\"
  \"<span class=sf-dump-key>Add new roles with customized permissions as per your requirement</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Add new roles with customized permissions as per your requirement</span>\"
  \"<span class=sf-dump-key>Add New Role</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Add New Role</span>\"
  \"<span class=sf-dump-key>Edit Role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Edit Role</span>\"
  \"<span class=sf-dump-key>Role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Role</span>\"
  \"<span class=sf-dump-key>Created At</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"
  \"<span class=sf-dump-key>Actions</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Actions</span>\"
  \"<span class=sf-dump-key>Search User</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Search User</span>\"
  \"<span class=sf-dump-key>Displaying _START_ to _END_ of _TOTAL_ entries</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Displaying _START_ to _END_ of _TOTAL_ entries</span>\"
  \"<span class=sf-dump-key>Showing _START_ to _END_ of _TOTAL_ entries</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Showing _START_ to _END_ of _TOTAL_ entries</span>\"
  \"<span class=sf-dump-key>Search...</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Search...</span>\"
  \"<span class=sf-dump-key>add new role</span>\" => \"<span class=sf-dump-str title=\"12 characters\">add new role</span>\"
  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">role</span>\"
  \"<span class=sf-dump-key>role name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role name</span>\"
  \"<span class=sf-dump-key>guard</span>\" => \"<span class=sf-dump-str title=\"5 characters\">guard</span>\"
  \"<span class=sf-dump-key>Administrator</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Administrator</span>\"
  \"<span class=sf-dump-key>Driver</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Driver</span>\"
  \"<span class=sf-dump-key>Details of</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Details of</span>\"
  \"<span class=sf-dump-key>There is no Permissions found!</span>\" => \"<span class=sf-dump-str title=\"30 characters\">There is no Permissions found!</span>\"
  \"<span class=sf-dump-key>Error!! can not fiche any Permission</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Error!! can not fiche any Permission</span>\"
  \"<span class=sf-dump-key>permissions</span>\" => \"<span class=sf-dump-str title=\"11 characters\">permissions</span>\"
  \"<span class=sf-dump-key>Roles</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Roles</span>\"
  \"<span class=sf-dump-key>Role Name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Role Name</span>\"
  \"<span class=sf-dump-key>Guard</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Guard</span>\"
  \"<span class=sf-dump-key>Permissions</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Permissions</span>\"
  \"<span class=sf-dump-key>Home</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Home</span>\"
  \"<span class=sf-dump-key>Profile</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Profile</span>\"
  \"<span class=sf-dump-key>Messages</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Messages</span>\"
  \"<span class=sf-dump-key>Close</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Close</span>\"
  \"<span class=sf-dump-key>Submit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Submit</span>\"
  \"<span class=sf-dump-key>Users</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Users</span>\"
  \"<span class=sf-dump-key>Active Users</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Active Users</span>\"
  \"<span class=sf-dump-key>Inactive Users</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Inactive Users</span>\"
  \"<span class=sf-dump-key>Pending Users</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Pending Users</span>\"
  \"<span class=sf-dump-key>Add New User</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Add New User</span>\"
  \"<span class=sf-dump-key>User</span>\" => \"<span class=sf-dump-str title=\"4 characters\">User</span>\"
  \"<span class=sf-dump-key>Phone</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Phone</span>\"
  \"<span class=sf-dump-key>Status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"
  \"<span class=sf-dump-key>Reset Password</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Reset Password</span>\"
  \"<span class=sf-dump-key>Add new User</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Add new User</span>\"
  \"<span class=sf-dump-key>Edit User</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Edit User</span>\"
  \"<span class=sf-dump-key>Main</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Main</span>\"
  \"<span class=sf-dump-key>Additional</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Additional</span>\"
  \"<span class=sf-dump-key>Full Name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Full Name</span>\"
  \"<span class=sf-dump-key><EMAIL></span>\" => \"<span class=sf-dump-str title=\"19 characters\"><EMAIL></span>\"
  \"<span class=sf-dump-key>Enter phone number</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Enter phone number</span>\"
  \"<span class=sf-dump-key>Password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Password</span>\"
  \"<span class=sf-dump-key>Confirm Password</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Confirm Password</span>\"
  \"<span class=sf-dump-key>User Role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">User Role</span>\"
  \"<span class=sf-dump-key>Teams</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Teams</span>\"
  \"<span class=sf-dump-key>Customers</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Customers</span>\"
  \"<span class=sf-dump-key>Active Customers</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Active Customers</span>\"
  \"<span class=sf-dump-key>Unverified Customers</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Unverified Customers</span>\"
  \"<span class=sf-dump-key>Blocked Customers</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Blocked Customers</span>\"
  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"
  \"<span class=sf-dump-key>tags</span>\" => \"<span class=sf-dump-str title=\"4 characters\">tags</span>\"
  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
  \"<span class=sf-dump-key>created at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created at</span>\"
  \"<span class=sf-dump-key>actions</span>\" => \"<span class=sf-dump-str title=\"7 characters\">actions</span>\"
  \"<span class=sf-dump-key>Add New Customer</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Add New Customer</span>\"
  \"<span class=sf-dump-key>Customer Role</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Customer Role</span>\"
  \"<span class=sf-dump-key>Select Role</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Select Role</span>\"
  \"<span class=sf-dump-key>Company Info</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Info</span>\"
  \"<span class=sf-dump-key>Company Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"
  \"<span class=sf-dump-key>enter company name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">enter company name</span>\"
  \"<span class=sf-dump-key>Company Address</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Company Address</span>\"
  \"<span class=sf-dump-key>enter company address</span>\" => \"<span class=sf-dump-str title=\"21 characters\">enter company address</span>\"
  \"<span class=sf-dump-key>Tags</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"
  \"<span class=sf-dump-key>Select Template</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Select Template</span>\"
  \"<span class=sf-dump-key>-- Select Template</span>\" => \"<span class=sf-dump-str title=\"18 characters\">-- Select Template</span>\"
  \"<span class=sf-dump-key>--- Select Template</span>\" => \"<span class=sf-dump-str title=\"19 characters\">--- Select Template</span>\"
  \"<span class=sf-dump-key>Logistics</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Logistics</span>\"
  \"<span class=sf-dump-key>Fleet</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Fleet</span>\"
  \"<span class=sf-dump-key>Invoice</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Invoice</span>\"
  \"<span class=sf-dump-key>Preview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Preview</span>\"
  \"<span class=sf-dump-key>Add</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Add</span>\"
  \"<span class=sf-dump-key>Pages</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Pages</span>\"
  \"<span class=sf-dump-key>User Profile</span>\" => \"<span class=sf-dump-str title=\"12 characters\">User Profile</span>\"
  \"<span class=sf-dump-key>Projects</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Projects</span>\"
  \"<span class=sf-dump-key>Account Settings</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Account Settings</span>\"
  \"<span class=sf-dump-key>Account</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Account</span>\"
  \"<span class=sf-dump-key>Security</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Security</span>\"
  \"<span class=sf-dump-key>Billing &amp; Plans</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Billing &amp; Plans</span>\"
  \"<span class=sf-dump-key>Notifications</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Notifications</span>\"
  \"<span class=sf-dump-key>Connections</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Connections</span>\"
  \"<span class=sf-dump-key>FAQ</span>\" => \"<span class=sf-dump-str title=\"3 characters\">FAQ</span>\"
  \"<span class=sf-dump-key>Front Pages</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Front Pages</span>\"
  \"<span class=sf-dump-key>Payment</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Payment</span>\"
  \"<span class=sf-dump-key>Help Center</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Help Center</span>\"
  \"<span class=sf-dump-key>Landing</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Landing</span>\"
  \"<span class=sf-dump-key>Categories</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Categories</span>\"
  \"<span class=sf-dump-key>Article</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Article</span>\"
  \"<span class=sf-dump-key>Pricing</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Pricing</span>\"
  \"<span class=sf-dump-key>Error</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Error</span>\"
  \"<span class=sf-dump-key>Coming Soon</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Coming Soon</span>\"
  \"<span class=sf-dump-key>Under Maintenance</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Under Maintenance</span>\"
  \"<span class=sf-dump-key>Not Authorized</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Not Authorized</span>\"
  \"<span class=sf-dump-key>Authentications</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Authentications</span>\"
  \"<span class=sf-dump-key>Login</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Login</span>\"
  \"<span class=sf-dump-key>Register</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Register</span>\"
  \"<span class=sf-dump-key>Verify Email</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Verify Email</span>\"
  \"<span class=sf-dump-key>Forgot Password</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Forgot Password</span>\"
  \"<span class=sf-dump-key>Two Steps</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Two Steps</span>\"
  \"<span class=sf-dump-key>Basic</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Basic</span>\"
  \"<span class=sf-dump-key>Cover</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Cover</span>\"
  \"<span class=sf-dump-key>Multi-steps</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Multi-steps</span>\"
  \"<span class=sf-dump-key>Modal Examples</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Modal Examples</span>\"
  \"<span class=sf-dump-key>Wizard Examples</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Wizard Examples</span>\"
  \"<span class=sf-dump-key>Checkout</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Checkout</span>\"
  \"<span class=sf-dump-key>Property Listing</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Property Listing</span>\"
  \"<span class=sf-dump-key>Create Deal</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Create Deal</span>\"
  \"<span class=sf-dump-key>Icons</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Icons</span>\"
  \"<span class=sf-dump-key>Tabler</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Tabler</span>\"
  \"<span class=sf-dump-key>Fontawesome</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fontawesome</span>\"
  \"<span class=sf-dump-key>User interface</span>\" => \"<span class=sf-dump-str title=\"14 characters\">User interface</span>\"
  \"<span class=sf-dump-key>Accordion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Accordion</span>\"
  \"<span class=sf-dump-key>Alerts</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Alerts</span>\"
  \"<span class=sf-dump-key>App Brand</span>\" => \"<span class=sf-dump-str title=\"9 characters\">App Brand</span>\"
  \"<span class=sf-dump-key>Badges</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Badges</span>\"
  \"<span class=sf-dump-key>Buttons</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Buttons</span>\"
  \"<span class=sf-dump-key>Cards</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Cards</span>\"
  \"<span class=sf-dump-key>Advance</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Advance</span>\"
  \"<span class=sf-dump-key>Statistics</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Statistics</span>\"
  \"<span class=sf-dump-key>Analytics</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Analytics</span>\"
  \"<span class=sf-dump-key>Carousel</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Carousel</span>\"
  \"<span class=sf-dump-key>Collapse</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Collapse</span>\"
  \"<span class=sf-dump-key>Dropdowns</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Dropdowns</span>\"
  \"<span class=sf-dump-key>Footer</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Footer</span>\"
  \"<span class=sf-dump-key>List Groups</span>\" => \"<span class=sf-dump-str title=\"11 characters\">List Groups</span>\"
  \"<span class=sf-dump-key>Modals</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Modals</span>\"
  \"<span class=sf-dump-key>Menu</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Menu</span>\"
  \"<span class=sf-dump-key>Navbar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Navbar</span>\"
  \"<span class=sf-dump-key>Offcanvas</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Offcanvas</span>\"
  \"<span class=sf-dump-key>Pagination &amp; Breadcrumbs</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Pagination &amp; Breadcrumbs</span>\"
  \"<span class=sf-dump-key>Progress</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Progress</span>\"
  \"<span class=sf-dump-key>Spinners</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Spinners</span>\"
  \"<span class=sf-dump-key>Tabs &amp; Pills</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tabs &amp; Pills</span>\"
  \"<span class=sf-dump-key>Toasts</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Toasts</span>\"
  \"<span class=sf-dump-key>Tooltips &amp; Popovers</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Tooltips &amp; Popovers</span>\"
  \"<span class=sf-dump-key>Typography</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Typography</span>\"
  \"<span class=sf-dump-key>Extended UI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Extended UI</span>\"
  \"<span class=sf-dump-key>Avatar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Avatar</span>\"
  \"<span class=sf-dump-key>BlockUI</span>\" => \"<span class=sf-dump-str title=\"7 characters\">BlockUI</span>\"
  \"<span class=sf-dump-key>Drag &amp; Drop</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Drag &amp; Drop</span>\"
  \"<span class=sf-dump-key>Media Player</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Media Player</span>\"
  \"<span class=sf-dump-key>Perfect Scrollbar</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Perfect Scrollbar</span>\"
  \"<span class=sf-dump-key>Star Ratings</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Star Ratings</span>\"
  \"<span class=sf-dump-key>SweetAlert2</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SweetAlert2</span>\"
  \"<span class=sf-dump-key>Text Divider</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Text Divider</span>\"
  \"<span class=sf-dump-key>Timeline</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Timeline</span>\"
  \"<span class=sf-dump-key>Fullscreen</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Fullscreen</span>\"
  \"<span class=sf-dump-key>Tour</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tour</span>\"
  \"<span class=sf-dump-key>Treeview</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Treeview</span>\"
  \"<span class=sf-dump-key>Miscellaneous</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Miscellaneous</span>\"
  \"<span class=sf-dump-key>Misc</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Misc</span>\"
  \"<span class=sf-dump-key>Form Elements</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Form Elements</span>\"
  \"<span class=sf-dump-key>Basic Inputs</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Basic Inputs</span>\"
  \"<span class=sf-dump-key>Input groups</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Input groups</span>\"
  \"<span class=sf-dump-key>Custom Options</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Custom Options</span>\"
  \"<span class=sf-dump-key>Editors</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Editors</span>\"
  \"<span class=sf-dump-key>File Upload</span>\" => \"<span class=sf-dump-str title=\"11 characters\">File Upload</span>\"
  \"<span class=sf-dump-key>Pickers</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Pickers</span>\"
  \"<span class=sf-dump-key>Select &amp; Tags</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Select &amp; Tags</span>\"
  \"<span class=sf-dump-key>Sliders</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Sliders</span>\"
  \"<span class=sf-dump-key>Switches</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Switches</span>\"
  \"<span class=sf-dump-key>Extras</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Extras</span>\"
  \"<span class=sf-dump-key>Form Layouts</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Form Layouts</span>\"
  \"<span class=sf-dump-key>Vertical Form</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Vertical Form</span>\"
  \"<span class=sf-dump-key>Horizontal Form</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Horizontal Form</span>\"
  \"<span class=sf-dump-key>Sticky Actions</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Sticky Actions</span>\"
  \"<span class=sf-dump-key>Form Wizard</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Form Wizard</span>\"
  \"<span class=sf-dump-key>Numbered</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Numbered</span>\"
  \"<span class=sf-dump-key>Advanced</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Advanced</span>\"
  \"<span class=sf-dump-key>Forms</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Forms</span>\"
  \"<span class=sf-dump-key>Form Validation</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Form Validation</span>\"
  \"<span class=sf-dump-key>Tables</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Tables</span>\"
  \"<span class=sf-dump-key>Datatables</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Datatables</span>\"
  \"<span class=sf-dump-key>Extensions</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Extensions</span>\"
  \"<span class=sf-dump-key>Charts</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Charts</span>\"
  \"<span class=sf-dump-key>Apex Charts</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Apex Charts</span>\"
  \"<span class=sf-dump-key>ChartJS</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ChartJS</span>\"
  \"<span class=sf-dump-key>Leaflet Maps</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Leaflet Maps</span>\"
  \"<span class=sf-dump-key>Support</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Support</span>\"
  \"<span class=sf-dump-key>Documentation</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Documentation</span>\"
  \"<span class=sf-dump-key>Academy</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Academy</span>\"
  \"<span class=sf-dump-key>My Course</span>\" => \"<span class=sf-dump-str title=\"9 characters\">My Course</span>\"
  \"<span class=sf-dump-key>Course Details</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Course Details</span>\"
  \"<span class=sf-dump-key>Apps &amp; Pages</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Apps &amp; Pages</span>\"
  \"<span class=sf-dump-key>Components</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Components</span>\"
  \"<span class=sf-dump-key>Forms &amp; Tables</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Forms &amp; Tables</span>\"
  \"<span class=sf-dump-key>Charts &amp; Maps</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Charts &amp; Maps</span>\"
  \"<span class=sf-dump-key>Id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">Id</span>\"
  \"<span class=sf-dump-key>General</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"
  \"<span class=sf-dump-key>Template</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Template</span>\"
  \"<span class=sf-dump-key>Donut drag&#233;e jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.</span>\" => \"<span class=sf-dump-str title=\"204 characters\">Donut drag&#233;e jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.</span>\"
  \"<span class=sf-dump-key>Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.</span>\" => \"<span class=sf-dump-str title=\"122 characters\">Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.</span>\"
  \"<span class=sf-dump-key>Geo-fence</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Geo-fence</span>\"
  \"<span class=sf-dump-key>It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.</span>\" => \"<span class=sf-dump-str title=\"123 characters\">It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.</span>\"
  \"<span class=sf-dump-key>Add New Geo-fence</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Add New Geo-fence</span>\"
  \"<span class=sf-dump-key>&#128269; Search Team</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#128269; Search Team</span>\"
  \"<span class=sf-dump-key>Geofences</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Geofences</span>\"
  \"<span class=sf-dump-key>Add Geo-fence</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Add Geo-fence</span>\"
  \"<span class=sf-dump-key>Select Teams</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Select Teams</span>\"
  \"<span class=sf-dump-key>Name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"
  \"<span class=sf-dump-key>Enter name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Enter name</span>\"
  \"<span class=sf-dump-key>Description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"
  \"<span class=sf-dump-key>Enter description</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enter description</span>\"
  \"<span class=sf-dump-key>The role name is required.</span>\" => \"<span class=sf-dump-str title=\"26 characters\">The role name is required.</span>\"
  \"<span class=sf-dump-key>The role name has already been taken.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The role name has already been taken.</span>\"
  \"<span class=sf-dump-key>The guard field is required.</span>\" => \"<span class=sf-dump-str title=\"28 characters\">The guard field is required.</span>\"
  \"<span class=sf-dump-key>The selected guard is invalid.</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The selected guard is invalid.</span>\"
  \"<span class=sf-dump-key>At least one permission must be selected.</span>\" => \"<span class=sf-dump-str title=\"41 characters\">At least one permission must be selected.</span>\"
  \"<span class=sf-dump-key>Permissions must be an array.</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Permissions must be an array.</span>\"
  \"<span class=sf-dump-key>Error to Save Role</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Error to Save Role</span>\"
  \"<span class=sf-dump-key>Role Saved</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Role Saved</span>\"
  \"<span class=sf-dump-key>This role can not be deleted</span>\" => \"<span class=sf-dump-str title=\"28 characters\">This role can not be deleted</span>\"
  \"<span class=sf-dump-key>There are users connected with this role</span>\" => \"<span class=sf-dump-str title=\"40 characters\">There are users connected with this role</span>\"
  \"<span class=sf-dump-key>Error to delete role</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Error to delete role</span>\"
  \"<span class=sf-dump-key>Role deleted</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Role deleted</span>\"
  \"<span class=sf-dump-key>Rate</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Rate</span>\"
  \"<span class=sf-dump-key>Fixed</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Fixed</span>\"
  \"<span class=sf-dump-key>Commission Rate</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Commission Rate</span>\"
  \"<span class=sf-dump-key>Commission fixed Amount</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Commission fixed Amount</span>\"
  \"<span class=sf-dump-key>General Settings</span>\" => \"<span class=sf-dump-str title=\"16 characters\">General Settings</span>\"
  \"<span class=sf-dump-key>You can manage the main and vital settings of the platform from here, so be careful.</span>\" => \"<span class=sf-dump-str title=\"84 characters\">You can manage the main and vital settings of the platform from here, so be careful.</span>\"
  \"<span class=sf-dump-key>Templates</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Templates</span>\"
  \"<span class=sf-dump-key>Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more</span>\" => \"<span class=sf-dump-str title=\"134 characters\">Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more</span>\"
  \"<span class=sf-dump-key>Add New Template</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Add New Template</span>\"
  \"<span class=sf-dump-key>Template Name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Template Name</span>\"
  \"<span class=sf-dump-key>enter the Template name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">enter the Template name</span>\"
  \"<span class=sf-dump-key>Default Customer Template</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default Customer Template</span>\"
  \"<span class=sf-dump-key>Default Driver Template</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default Driver Template</span>\"
  \"<span class=sf-dump-key>Default User Template</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default User Template</span>\"
  \"<span class=sf-dump-key>Default Task Template</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default Task Template</span>\"
  \"<span class=sf-dump-key>Drivers Commission</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Drivers Commission</span>\"
  \"<span class=sf-dump-key>Commission Type</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Commission Type</span>\"
  \"<span class=sf-dump-key>The user id is required.</span>\" => \"<span class=sf-dump-str title=\"24 characters\">The user id is required.</span>\"
  \"<span class=sf-dump-key>The selected user does not exist.</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The selected user does not exist.</span>\"
  \"<span class=sf-dump-key>The status field is required.</span>\" => \"<span class=sf-dump-str title=\"29 characters\">The status field is required.</span>\"
  \"<span class=sf-dump-key>Error to Change user Status</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Error to Change user Status</span>\"
  \"<span class=sf-dump-key>User Status changed</span>\" => \"<span class=sf-dump-str title=\"19 characters\">User Status changed</span>\"
  \"<span class=sf-dump-key>The name field is required.</span>\" => \"<span class=sf-dump-str title=\"27 characters\">The name field is required.</span>\"
  \"<span class=sf-dump-key>The email field is required.</span>\" => \"<span class=sf-dump-str title=\"28 characters\">The email field is required.</span>\"
  \"<span class=sf-dump-key>The email has already been taken.</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The email has already been taken.</span>\"
  \"<span class=sf-dump-key>The phone field is required.</span>\" => \"<span class=sf-dump-str title=\"28 characters\">The phone field is required.</span>\"
  \"<span class=sf-dump-key>The phone has already been taken.</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The phone has already been taken.</span>\"
  \"<span class=sf-dump-key>The password field is required.</span>\" => \"<span class=sf-dump-str title=\"31 characters\">The password field is required.</span>\"
  \"<span class=sf-dump-key>The password and confirmation must match.</span>\" => \"<span class=sf-dump-str title=\"41 characters\">The password and confirmation must match.</span>\"
  \"<span class=sf-dump-key>The user role is required.</span>\" => \"<span class=sf-dump-str title=\"26 characters\">The user role is required.</span>\"
  \"<span class=sf-dump-key>The selected role is invalid.</span>\" => \"<span class=sf-dump-str title=\"29 characters\">The selected role is invalid.</span>\"
  \"<span class=sf-dump-key>Teams must be an array.</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Teams must be an array.</span>\"
  \"<span class=sf-dump-key>Customers must be an array.</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Customers must be an array.</span>\"
  \"<span class=sf-dump-key>The selected template is invalid.</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The selected template is invalid.</span>\"
  \"<span class=sf-dump-key>The :label field is required.</span>\" => \"<span class=sf-dump-str title=\"29 characters\">The :label field is required.</span>\"
  \"<span class=sf-dump-key>Can not find the selected user</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Can not find the selected user</span>\"
  \"<span class=sf-dump-key>User saved successfully</span>\" => \"<span class=sf-dump-str title=\"23 characters\">User saved successfully</span>\"
  \"<span class=sf-dump-key>User not found</span>\" => \"<span class=sf-dump-str title=\"14 characters\">User not found</span>\"
  \"<span class=sf-dump-key>Error to change reset password  status</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Error to change reset password  status</span>\"
  \"<span class=sf-dump-key>User deleted</span>\" => \"<span class=sf-dump-str title=\"12 characters\">User deleted</span>\"
  \"<span class=sf-dump-key>The selected User has teams to mange. you can not delete hem right now</span>\" => \"<span class=sf-dump-str title=\"70 characters\">The selected User has teams to mange. you can not delete hem right now</span>\"
  \"<span class=sf-dump-key>Error to delete User</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Error to delete User</span>\"
  \"<span class=sf-dump-key>Vehicles</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Vehicles</span>\"
  \"<span class=sf-dump-key>Managing the types of vehicles and trucks that will provide delivery services on the platform</span>\" => \"<span class=sf-dump-str title=\"93 characters\">Managing the types of vehicles and trucks that will provide delivery services on the platform</span>\"
  \"<span class=sf-dump-key>Vehicles Types</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Vehicles Types</span>\"
  \"<span class=sf-dump-key>Vehicles Sizes</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Vehicles Sizes</span>\"
  \"<span class=sf-dump-key>vehicle name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">vehicle name</span>\"
  \"<span class=sf-dump-key>English name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">English name</span>\"
  \"<span class=sf-dump-key>save</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"
  \"<span class=sf-dump-key>types</span>\" => \"<span class=sf-dump-str title=\"5 characters\">types</span>\"
  \"<span class=sf-dump-key>Select vehicle</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Select vehicle</span>\"
  \"<span class=sf-dump-key>select vehicle</span>\" => \"<span class=sf-dump-str title=\"14 characters\">select vehicle</span>\"
  \"<span class=sf-dump-key>Flitter by vehicle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Flitter by vehicle</span>\"
  \"<span class=sf-dump-key>all vehicle</span>\" => \"<span class=sf-dump-str title=\"11 characters\">all vehicle</span>\"
  \"<span class=sf-dump-key>vehicle</span>\" => \"<span class=sf-dump-str title=\"7 characters\">vehicle</span>\"
  \"<span class=sf-dump-key>type name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">type name</span>\"
  \"<span class=sf-dump-key>sizes</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sizes</span>\"
  \"<span class=sf-dump-key>size</span>\" => \"<span class=sf-dump-str title=\"4 characters\">size</span>\"
  \"<span class=sf-dump-key>Select vehicle Type</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Select vehicle Type</span>\"
  \"<span class=sf-dump-key>Flitter by vehicle type</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Flitter by vehicle type</span>\"
  \"<span class=sf-dump-key>select vehicle type</span>\" => \"<span class=sf-dump-str title=\"19 characters\">select vehicle type</span>\"
  \"<span class=sf-dump-key>vehicle type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">vehicle type</span>\"
  \"<span class=sf-dump-key>No data available</span>\" => \"<span class=sf-dump-str title=\"17 characters\">No data available</span>\"
  \"<span class=sf-dump-key>select vehicle Size</span>\" => \"<span class=sf-dump-str title=\"19 characters\">select vehicle Size</span>\"
  \"<span class=sf-dump-key>Add New Tag</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Add New Tag</span>\"
  \"<span class=sf-dump-key>tag name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tag name</span>\"
  \"<span class=sf-dump-key>enter the tag name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">enter the tag name</span>\"
  \"<span class=sf-dump-key>tag slug</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tag slug</span>\"
  \"<span class=sf-dump-key>enter the tag slug</span>\" => \"<span class=sf-dump-str title=\"18 characters\">enter the tag slug</span>\"
  \"<span class=sf-dump-key>Select Tags</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Select Tags</span>\"
  \"<span class=sf-dump-key>The tag name is required.</span>\" => \"<span class=sf-dump-str title=\"25 characters\">The tag name is required.</span>\"
  \"<span class=sf-dump-key>The tag name has already been taken.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">The tag name has already been taken.</span>\"
  \"<span class=sf-dump-key>The tag slug is required.</span>\" => \"<span class=sf-dump-str title=\"25 characters\">The tag slug is required.</span>\"
  \"<span class=sf-dump-key>The tag slug has already been taken.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">The tag slug has already been taken.</span>\"
  \"<span class=sf-dump-key>The description must be a string.</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The description must be a string.</span>\"
  \"<span class=sf-dump-key>The description may not be greater than 400 characters.</span>\" => \"<span class=sf-dump-str title=\"55 characters\">The description may not be greater than 400 characters.</span>\"
  \"<span class=sf-dump-key>Can not find the selected Tag</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Can not find the selected Tag</span>\"
  \"<span class=sf-dump-key>Error: can not save the Tag</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Error: can not save the Tag</span>\"
  \"<span class=sf-dump-key>Tag saved successfully</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Tag saved successfully</span>\"
  \"<span class=sf-dump-key>Error to find selected Tag</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Error to find selected Tag</span>\"
  \"<span class=sf-dump-key>Error to delete Tag</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Error to delete Tag</span>\"
  \"<span class=sf-dump-key>Tag deleted</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tag deleted</span>\"
  \"<span class=sf-dump-key>The geofence name is required.</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The geofence name is required.</span>\"
  \"<span class=sf-dump-key>The geofence name has already been taken.</span>\" => \"<span class=sf-dump-str title=\"41 characters\">The geofence name has already been taken.</span>\"
  \"<span class=sf-dump-key>The coordinates field is required.</span>\" => \"<span class=sf-dump-str title=\"34 characters\">The coordinates field is required.</span>\"
  \"<span class=sf-dump-key>The coordinates must be a string.</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The coordinates must be a string.</span>\"
  \"<span class=sf-dump-key>Can not find the selected Geo-Fence</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Can not find the selected Geo-Fence</span>\"
  \"<span class=sf-dump-key>error to save Geo-Fence</span>\" => \"<span class=sf-dump-str title=\"23 characters\">error to save Geo-Fence</span>\"
  \"<span class=sf-dump-key>Geo-Fence saved successfully</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Geo-Fence saved successfully</span>\"
  \"<span class=sf-dump-key>Error to delete Geo-fence</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Error to delete Geo-fence</span>\"
  \"<span class=sf-dump-key>Geo-fence deleted</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Geo-fence deleted</span>\"
  \"<span class=sf-dump-key>Points</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Points</span>\"
  \"<span class=sf-dump-key>Add New Point</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Add New Point</span>\"
  \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"7 characters\">address</span>\"
  \"<span class=sf-dump-key>customer</span>\" => \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"
  \"<span class=sf-dump-key>enter the point name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">enter the point name</span>\"
  \"<span class=sf-dump-key>enter the point address</span>\" => \"<span class=sf-dump-str title=\"23 characters\">enter the point address</span>\"
  \"<span class=sf-dump-key>Location</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Location</span>\"
  \"<span class=sf-dump-key>confirm location</span>\" => \"<span class=sf-dump-str title=\"16 characters\">confirm location</span>\"
  \"<span class=sf-dump-key>Contact name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Contact name</span>\"
  \"<span class=sf-dump-key>enter the point contact name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">enter the point contact name</span>\"
  \"<span class=sf-dump-key>Contact phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Contact phone</span>\"
  \"<span class=sf-dump-key>enter the point contact phone</span>\" => \"<span class=sf-dump-str title=\"29 characters\">enter the point contact phone</span>\"
  \"<span class=sf-dump-key>Select Customer</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Select Customer</span>\"
  \"<span class=sf-dump-key>The point name is required.</span>\" => \"<span class=sf-dump-str title=\"27 characters\">The point name is required.</span>\"
  \"<span class=sf-dump-key>The point name must be a string.</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The point name must be a string.</span>\"
  \"<span class=sf-dump-key>The contact name must be a string.</span>\" => \"<span class=sf-dump-str title=\"34 characters\">The contact name must be a string.</span>\"
  \"<span class=sf-dump-key>The contact name may not be greater than 400 characters.</span>\" => \"<span class=sf-dump-str title=\"56 characters\">The contact name may not be greater than 400 characters.</span>\"
  \"<span class=sf-dump-key>The contact phone must be a string.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The contact phone must be a string.</span>\"
  \"<span class=sf-dump-key>The contact phone may not be greater than 50 characters.</span>\" => \"<span class=sf-dump-str title=\"56 characters\">The contact phone may not be greater than 50 characters.</span>\"
  \"<span class=sf-dump-key>The address field is required.</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The address field is required.</span>\"
  \"<span class=sf-dump-key>The address must be a string.</span>\" => \"<span class=sf-dump-str title=\"29 characters\">The address must be a string.</span>\"
  \"<span class=sf-dump-key>The address may not be greater than 500 characters.</span>\" => \"<span class=sf-dump-str title=\"51 characters\">The address may not be greater than 500 characters.</span>\"
  \"<span class=sf-dump-key>The latitude field is required.</span>\" => \"<span class=sf-dump-str title=\"31 characters\">The latitude field is required.</span>\"
  \"<span class=sf-dump-key>The latitude must be a number.</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The latitude must be a number.</span>\"
  \"<span class=sf-dump-key>The longitude field is required.</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The longitude field is required.</span>\"
  \"<span class=sf-dump-key>The longitude must be a number.</span>\" => \"<span class=sf-dump-str title=\"31 characters\">The longitude must be a number.</span>\"
  \"<span class=sf-dump-key>The selected customer is invalid.</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The selected customer is invalid.</span>\"
  \"<span class=sf-dump-key>Can not find the selected Point</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Can not find the selected Point</span>\"
  \"<span class=sf-dump-key>Error: can not save the Point</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Error: can not save the Point</span>\"
  \"<span class=sf-dump-key>Point saved successfully</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Point saved successfully</span>\"
  \"<span class=sf-dump-key>Error to delete Point. its connect with pricing mater</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Error to delete Point. its connect with pricing mater</span>\"
  \"<span class=sf-dump-key>Error to delete Point</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Error to delete Point</span>\"
  \"<span class=sf-dump-key>Point deleted</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Point deleted</span>\"
  \"<span class=sf-dump-key>Blockages</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Blockages</span>\"
  \"<span class=sf-dump-key>Add a new Blockage</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Add a new Blockage</span>\"
  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"
  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
  \"<span class=sf-dump-key>coordinates</span>\" => \"<span class=sf-dump-str title=\"11 characters\">coordinates</span>\"
  \"<span class=sf-dump-key>Block Type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Block Type</span>\"
  \"<span class=sf-dump-key>Select Type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Select Type</span>\"
  \"<span class=sf-dump-key>Point Closed</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Point Closed</span>\"
  \"<span class=sf-dump-key>Line Closed</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Line Closed</span>\"
  \"<span class=sf-dump-key>Block Description</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Block Description</span>\"
  \"<span class=sf-dump-key>optional</span>\" => \"<span class=sf-dump-str title=\"8 characters\">optional</span>\"
  \"<span class=sf-dump-key>draw the Points on the map</span>\" => \"<span class=sf-dump-str title=\"26 characters\">draw the Points on the map</span>\"
  \"<span class=sf-dump-key>Save</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Save</span>\"
  \"<span class=sf-dump-key>Cancel</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Cancel</span>\"
  \"<span class=sf-dump-key>The blockage type is required.</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The blockage type is required.</span>\"
  \"<span class=sf-dump-key>The selected blockage type is invalid.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">The selected blockage type is invalid.</span>\"
  \"<span class=sf-dump-key>Error: can not save the Blockage</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Error: can not save the Blockage</span>\"
  \"<span class=sf-dump-key>Blockage saved successfully</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Blockage saved successfully</span>\"
  \"<span class=sf-dump-key>Error to delete Blockage</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Error to delete Blockage</span>\"
  \"<span class=sf-dump-key>Blockage deleted</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Blockage deleted</span>\"
  \"<span class=sf-dump-key>Pricing Methods</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Pricing Methods</span>\"
  \"<span class=sf-dump-key>Method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Method</span>\"
  \"<span class=sf-dump-key>Add New Method</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Add New Method</span>\"
  \"<span class=sf-dump-key>Method Name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Method Name</span>\"
  \"<span class=sf-dump-key>enter the Method name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">enter the Method name</span>\"
  \"<span class=sf-dump-key>Edit Method</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Edit Method</span>\"
  \"<span class=sf-dump-key>The method name is required.</span>\" => \"<span class=sf-dump-str title=\"28 characters\">The method name is required.</span>\"
  \"<span class=sf-dump-key>The method name has already been taken.</span>\" => \"<span class=sf-dump-str title=\"39 characters\">The method name has already been taken.</span>\"
  \"<span class=sf-dump-key>The description field is required.</span>\" => \"<span class=sf-dump-str title=\"34 characters\">The description field is required.</span>\"
  \"<span class=sf-dump-key>Can not find the selected Pricing Method</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Can not find the selected Pricing Method</span>\"
  \"<span class=sf-dump-key>error to save Pricing Method</span>\" => \"<span class=sf-dump-str title=\"28 characters\">error to save Pricing Method</span>\"
  \"<span class=sf-dump-key>Pricing Method saved successfully</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Pricing Method saved successfully</span>\"
  \"<span class=sf-dump-key>Pricing Method not found</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Pricing Method not found</span>\"
  \"<span class=sf-dump-key>Error to change Pricing Method status</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Error to change Pricing Method status</span>\"
  \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">label</span>\"
  \"<span class=sf-dump-key>driver can</span>\" => \"<span class=sf-dump-str title=\"10 characters\">driver can</span>\"
  \"<span class=sf-dump-key>customer can</span>\" => \"<span class=sf-dump-str title=\"12 characters\">customer can</span>\"
  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"5 characters\">value</span>\"
  \"<span class=sf-dump-key>require</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"
  \"<span class=sf-dump-key>Select Values</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Select Values</span>\"
  \"<span class=sf-dump-key>Value</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Value</span>\"
  \"<span class=sf-dump-key>Display Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Display Name</span>\"
  \"<span class=sf-dump-key>Action</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Action</span>\"
  \"<span class=sf-dump-key>More</span>\" => \"<span class=sf-dump-str title=\"4 characters\">More</span>\"
  \"<span class=sf-dump-key>Customers Selections</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Customers Selections</span>\"
  \"<span class=sf-dump-key>Apply to All Customers</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Apply to All Customers</span>\"
  \"<span class=sf-dump-key>Use customers tags</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Use customers tags</span>\"
  \"<span class=sf-dump-key>Use Specific customers</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Use Specific customers</span>\"
  \"<span class=sf-dump-key>Vehicles Selections</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Vehicles Selections</span>\"
  \"<span class=sf-dump-key>Base Fare</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Base Fare</span>\"
  \"<span class=sf-dump-key>Base Distance</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Base Distance</span>\"
  \"<span class=sf-dump-key>Base Waiting</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Base Waiting</span>\"
  \"<span class=sf-dump-key>Distance Fare</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Distance Fare</span>\"
  \"<span class=sf-dump-key>Waiting Fare</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Waiting Fare</span>\"
  \"<span class=sf-dump-key>Dynamic Pricing Based on Field Values</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Dynamic Pricing Based on Field Values</span>\"
  \"<span class=sf-dump-key>add field</span>\" => \"<span class=sf-dump-str title=\"9 characters\">add field</span>\"
  \"<span class=sf-dump-key>Dynamic Pricing Based on Geo-fence</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Dynamic Pricing Based on Geo-fence</span>\"
  \"<span class=sf-dump-key>add geofence</span>\" => \"<span class=sf-dump-str title=\"12 characters\">add geofence</span>\"
  \"<span class=sf-dump-key>Commission</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Commission</span>\"
  \"<span class=sf-dump-key>VAT Commission</span>\" => \"<span class=sf-dump-str title=\"14 characters\">VAT Commission</span>\"
  \"<span class=sf-dump-key>Service Tax Commission</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Service Tax Commission</span>\"
  \"<span class=sf-dump-key>Discount Fare</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Discount Fare</span>\"
  \"<span class=sf-dump-key>Discount percentage %</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Discount percentage %</span>\"
  \"<span class=sf-dump-key>The rule name is required.</span>\" => \"<span class=sf-dump-str title=\"26 characters\">The rule name is required.</span>\"
  \"<span class=sf-dump-key>The rule name must be a string.</span>\" => \"<span class=sf-dump-str title=\"31 characters\">The rule name must be a string.</span>\"
  \"<span class=sf-dump-key>The rule name may not be greater than 255 characters.</span>\" => \"<span class=sf-dump-str title=\"53 characters\">The rule name may not be greater than 255 characters.</span>\"
  \"<span class=sf-dump-key>The rule name has already been taken.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The rule name has already been taken.</span>\"
  \"<span class=sf-dump-key>The decimal places field is required.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The decimal places field is required.</span>\"
  \"<span class=sf-dump-key>The decimal places must be an integer.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">The decimal places must be an integer.</span>\"
  \"<span class=sf-dump-key>The decimal places must be at least 0.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">The decimal places must be at least 0.</span>\"
  \"<span class=sf-dump-key>The decimal places may not be greater than 10.</span>\" => \"<span class=sf-dump-str title=\"46 characters\">The decimal places may not be greater than 10.</span>\"
  \"<span class=sf-dump-key>The form template is required.</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The form template is required.</span>\"
  \"<span class=sf-dump-key>The form template id must be an integer.</span>\" => \"<span class=sf-dump-str title=\"40 characters\">The form template id must be an integer.</span>\"
  \"<span class=sf-dump-key>The selected form template is invalid.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">The selected form template is invalid.</span>\"
  \"<span class=sf-dump-key>At least one customer must be selected.</span>\" => \"<span class=sf-dump-str title=\"39 characters\">At least one customer must be selected.</span>\"
  \"<span class=sf-dump-key>Each customer is required.</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Each customer is required.</span>\"
  \"<span class=sf-dump-key>Each customer id must be an integer.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Each customer id must be an integer.</span>\"
  \"<span class=sf-dump-key>At least one tag must be selected.</span>\" => \"<span class=sf-dump-str title=\"34 characters\">At least one tag must be selected.</span>\"
  \"<span class=sf-dump-key>Tags must be an array.</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Tags must be an array.</span>\"
  \"<span class=sf-dump-key>Each tag is required.</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Each tag is required.</span>\"
  \"<span class=sf-dump-key>Each tag id must be an integer.</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Each tag id must be an integer.</span>\"
  \"<span class=sf-dump-key>The selected tag is invalid.</span>\" => \"<span class=sf-dump-str title=\"28 characters\">The selected tag is invalid.</span>\"
  \"<span class=sf-dump-key>At least one vehicle size must be selected.</span>\" => \"<span class=sf-dump-str title=\"43 characters\">At least one vehicle size must be selected.</span>\"
  \"<span class=sf-dump-key>Sizes must be an array.</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Sizes must be an array.</span>\"
  \"<span class=sf-dump-key>Each size id must be an integer.</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Each size id must be an integer.</span>\"
  \"<span class=sf-dump-key>The selected vehicle size is invalid.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The selected vehicle size is invalid.</span>\"
  \"<span class=sf-dump-key>The base fare field is required.</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The base fare field is required.</span>\"
  \"<span class=sf-dump-key>The base fare must be a number.</span>\" => \"<span class=sf-dump-str title=\"31 characters\">The base fare must be a number.</span>\"
  \"<span class=sf-dump-key>The base fare must be at least 0.</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The base fare must be at least 0.</span>\"
  \"<span class=sf-dump-key>The base distance field is required.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">The base distance field is required.</span>\"
  \"<span class=sf-dump-key>The base distance must be a number.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The base distance must be a number.</span>\"
  \"<span class=sf-dump-key>The base distance must be at least 0.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The base distance must be at least 0.</span>\"
  \"<span class=sf-dump-key>The base waiting field is required.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The base waiting field is required.</span>\"
  \"<span class=sf-dump-key>The base waiting must be a number.</span>\" => \"<span class=sf-dump-str title=\"34 characters\">The base waiting must be a number.</span>\"
  \"<span class=sf-dump-key>The base waiting must be at least 0.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">The base waiting must be at least 0.</span>\"
  \"<span class=sf-dump-key>The distance fare field is required.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">The distance fare field is required.</span>\"
  \"<span class=sf-dump-key>The distance fare must be a number.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The distance fare must be a number.</span>\"
  \"<span class=sf-dump-key>The distance fare must be at least 0.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The distance fare must be at least 0.</span>\"
  \"<span class=sf-dump-key>The waiting fare field is required.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The waiting fare field is required.</span>\"
  \"<span class=sf-dump-key>The waiting fare must be a number.</span>\" => \"<span class=sf-dump-str title=\"34 characters\">The waiting fare must be a number.</span>\"
  \"<span class=sf-dump-key>The waiting fare must be at least 0.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">The waiting fare must be at least 0.</span>\"
  \"<span class=sf-dump-key>The VAT commission field is required.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The VAT commission field is required.</span>\"
  \"<span class=sf-dump-key>The VAT commission must be a number.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">The VAT commission must be a number.</span>\"
  \"<span class=sf-dump-key>The VAT commission must be at least 0.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">The VAT commission must be at least 0.</span>\"
  \"<span class=sf-dump-key>The VAT commission may not be greater than 100.</span>\" => \"<span class=sf-dump-str title=\"47 characters\">The VAT commission may not be greater than 100.</span>\"
  \"<span class=sf-dump-key>The service commission field is required.</span>\" => \"<span class=sf-dump-str title=\"41 characters\">The service commission field is required.</span>\"
  \"<span class=sf-dump-key>The service commission must be a number.</span>\" => \"<span class=sf-dump-str title=\"40 characters\">The service commission must be a number.</span>\"
  \"<span class=sf-dump-key>The service commission must be at least 0.</span>\" => \"<span class=sf-dump-str title=\"42 characters\">The service commission must be at least 0.</span>\"
  \"<span class=sf-dump-key>The service commission may not be greater than 100.</span>\" => \"<span class=sf-dump-str title=\"51 characters\">The service commission may not be greater than 100.</span>\"
  \"<span class=sf-dump-key>The discount must be a number.</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The discount must be a number.</span>\"
  \"<span class=sf-dump-key>The discount must be at least 0.</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The discount must be at least 0.</span>\"
  \"<span class=sf-dump-key>The discount may not be greater than 100.</span>\" => \"<span class=sf-dump-str title=\"41 characters\">The discount may not be greater than 100.</span>\"
  \"<span class=sf-dump-key>Task Done</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Task Done</span>\"
  \"<span class=sf-dump-key>Running Tasks</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Running Tasks</span>\"
  \"<span class=sf-dump-key>Details</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Details</span>\"
  \"<span class=sf-dump-key>Edit</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Edit</span>\"
  \"<span class=sf-dump-key>Suspend</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Suspend</span>\"
  \"<span class=sf-dump-key>Tasks</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Tasks</span>\"
  \"<span class=sf-dump-key>Total</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Total</span>\"
  \"<span class=sf-dump-key>Issued Date</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Issued Date</span>\"
  \"<span class=sf-dump-key>Additional Fields</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Additional Fields</span>\"
  \"<span class=sf-dump-key>No additional data found for this customer.</span>\" => \"<span class=sf-dump-str title=\"43 characters\">No additional data found for this customer.</span>\"
  \"<span class=sf-dump-key>View</span>\" => \"<span class=sf-dump-str title=\"4 characters\">View</span>\"
  \"<span class=sf-dump-key>Change Status</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Change Status</span>\"
  \"<span class=sf-dump-key>Create Wallet</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Create Wallet</span>\"
  \"<span class=sf-dump-key>The customer id is required.</span>\" => \"<span class=sf-dump-str title=\"28 characters\">The customer id is required.</span>\"
  \"<span class=sf-dump-key>The selected customer does not exist.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The selected customer does not exist.</span>\"
  \"<span class=sf-dump-key>You do not have permission to do actions to this record</span>\" => \"<span class=sf-dump-str title=\"55 characters\">You do not have permission to do actions to this record</span>\"
  \"<span class=sf-dump-key>Error to Change Customer Status</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Error to Change Customer Status</span>\"
  \"<span class=sf-dump-key>Customer Status changed</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Customer Status changed</span>\"
  \"<span class=sf-dump-key>Can not find the selected Customer</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Can not find the selected Customer</span>\"
  \"<span class=sf-dump-key>Error: can not save the Customer</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Error: can not save the Customer</span>\"
  \"<span class=sf-dump-key>Customer saved successfully</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Customer saved successfully</span>\"
  \"<span class=sf-dump-key>Error to delete Customer</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Error to delete Customer</span>\"
  \"<span class=sf-dump-key>Customer deleted</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Customer deleted</span>\"
  \"<span class=sf-dump-key>The driver id is required.</span>\" => \"<span class=sf-dump-str title=\"26 characters\">The driver id is required.</span>\"
  \"<span class=sf-dump-key>The selected driver does not exist.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The selected driver does not exist.</span>\"
  \"<span class=sf-dump-key>Error to Change Driver Status</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Error to Change Driver Status</span>\"
  \"<span class=sf-dump-key>Driver Status changed</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Driver Status changed</span>\"
  \"<span class=sf-dump-key>Can not find the selected Driver</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Can not find the selected Driver</span>\"
  \"<span class=sf-dump-key>Error: can not save the Driver</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Error: can not save the Driver</span>\"
  \"<span class=sf-dump-key>Driver saved successfully</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Driver saved successfully</span>\"
  \"<span class=sf-dump-key>Error to delete Driver</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Error to delete Driver</span>\"
  \"<span class=sf-dump-key>Driver deleted</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Driver deleted</span>\"
  \"<span class=sf-dump-key>Drivers</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Drivers</span>\"
  \"<span class=sf-dump-key>Active Drivers</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Active Drivers</span>\"
  \"<span class=sf-dump-key>Pending Drivers</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Pending Drivers</span>\"
  \"<span class=sf-dump-key>Blocked Drivers</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Blocked Drivers</span>\"
  \"<span class=sf-dump-key>Unverified Drivers</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Unverified Drivers</span>\"
  \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"8 characters\">username</span>\"
  \"<span class=sf-dump-key>Add new Driver</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Add new Driver</span>\"
  \"<span class=sf-dump-key>Username</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Username</span>\"
  \"<span class=sf-dump-key>Team</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Team</span>\"
  \"<span class=sf-dump-key>Select Team</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Select Team</span>\"
  \"<span class=sf-dump-key>Driver Role</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Driver Role</span>\"
  \"<span class=sf-dump-key>Home Address</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Home Address</span>\"
  \"<span class=sf-dump-key>enter home address</span>\" => \"<span class=sf-dump-str title=\"18 characters\">enter home address</span>\"
  \"<span class=sf-dump-key>Select Commission Type</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Select Commission Type</span>\"
  \"<span class=sf-dump-key>&#1612;Rate</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Rate</span>\"
  \"<span class=sf-dump-key>Fixed Amount</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Fixed Amount</span>\"
  \"<span class=sf-dump-key>Subscription Monthly</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Subscription Monthly</span>\"
  \"<span class=sf-dump-key>Commission Amount</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Commission Amount</span>\"
  \"<span class=sf-dump-key>Vehicle Selection</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Vehicle Selection</span>\"
  \"<span class=sf-dump-key>Wallets</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Wallets</span>\"
  \"<span class=sf-dump-key>balance</span>\" => \"<span class=sf-dump-str title=\"7 characters\">balance</span>\"
  \"<span class=sf-dump-key>Debt Ceiling</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Debt Ceiling</span>\"
  \"<span class=sf-dump-key>preview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">preview</span>\"
  \"<span class=sf-dump-key>last transaction</span>\" => \"<span class=sf-dump-str title=\"16 characters\">last transaction</span>\"
  \"<span class=sf-dump-key>Amount</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Amount</span>\"
  \"<span class=sf-dump-key>Maturity</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Maturity</span>\"
  \"<span class=sf-dump-key>Task</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Task</span>\"
  \"<span class=sf-dump-key>Add New Transaction</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Add New Transaction</span>\"
  \"<span class=sf-dump-key>Enter the amount</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Enter the amount</span>\"
  \"<span class=sf-dump-key>Transaction Type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Transaction Type</span>\"
  \"<span class=sf-dump-key>Credit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Credit</span>\"
  \"<span class=sf-dump-key>Debit</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Debit</span>\"
  \"<span class=sf-dump-key>Maturity Time</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Maturity Time</span>\"
  \"<span class=sf-dump-key>Optional notes...</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Optional notes...</span>\"
  \"<span class=sf-dump-key>Image </span>\" => \"<span class=sf-dump-str title=\"6 characters\">Image </span>\"
  \"<span class=sf-dump-key>View the image</span>\" => \"<span class=sf-dump-str title=\"14 characters\">View the image</span>\"
  \"<span class=sf-dump-key>close</span>\" => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"
  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1838441746\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","translationsB":"<pre class=sf-dump id=sf-dump-1073181822 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"24393 characters\">{&quot;Dashboards&quot;:&quot;Dashboards&quot;,&quot;Dashboard&quot;:&quot;Dashboard&quot;,&quot;eCommerce&quot;:&quot;eCommerce&quot;,&quot;CRM&quot;:&quot;CRM&quot;,&quot;Layouts&quot;:&quot;Layouts&quot;,&quot;Collapsed menu&quot;:&quot;Collapsed menu&quot;,&quot;Content navbar&quot;:&quot;Content navbar&quot;,&quot;Content nav + Sidebar&quot;:&quot;Content nav + Sidebar&quot;,&quot;Horizontal&quot;:&quot;Horizontal&quot;,&quot;Vertical&quot;:&quot;Vertical&quot;,&quot;Without menu&quot;:&quot;Without menu&quot;,&quot;Without navbar&quot;:&quot;Without navbar&quot;,&quot;Fluid&quot;:&quot;Fluid&quot;,&quot;Container&quot;:&quot;Container&quot;,&quot;Blank&quot;:&quot;Blank&quot;,&quot;Laravel Example&quot;:&quot;Laravel Example&quot;,&quot;User Management&quot;:&quot;User Management&quot;,&quot;Apps&quot;:&quot;Apps&quot;,&quot;Email&quot;:&quot;Email&quot;,&quot;Chat&quot;:&quot;Chat&quot;,&quot;Calendar&quot;:&quot;Calendar&quot;,&quot;Kanban&quot;:&quot;Kanban&quot;,&quot;Products&quot;:&quot;Products&quot;,&quot;Add Product&quot;:&quot;Add Product&quot;,&quot;Product List&quot;:&quot;Product List&quot;,&quot;Category List&quot;:&quot;Category List&quot;,&quot;Category&quot;:&quot;Category&quot;,&quot;Order&quot;:&quot;Order&quot;,&quot;Order List&quot;:&quot;Order List&quot;,&quot;Order Details&quot;:&quot;Order Details&quot;,&quot;Customer&quot;:&quot;Customer&quot;,&quot;All Customer&quot;:&quot;All Customer&quot;,&quot;All Customers&quot;:&quot;All Customers&quot;,&quot;Customer Details&quot;:&quot;Customer Details&quot;,&quot;Overview&quot;:&quot;Overview&quot;,&quot;Address &amp; Billing&quot;:&quot;Address &amp; Billing&quot;,&quot;Manage Reviews&quot;:&quot;Manage Reviews&quot;,&quot;Referrals&quot;:&quot;Referrals&quot;,&quot;Settings&quot;:&quot;Settings&quot;,&quot;Store Details&quot;:&quot;Store Details&quot;,&quot;Payments&quot;:&quot;Payments&quot;,&quot;Shipping &amp; Delivery&quot;:&quot;Shipping &amp; Delivery&quot;,&quot;Locations&quot;:&quot;Locations&quot;,&quot;Roles &amp; Permissions&quot;:&quot;Roles &amp; Permissions&quot;,&quot;Add new roles with customized permissions as per your requirement&quot;:&quot;Add new roles with customized permissions as per your requirement&quot;,&quot;Add New Role&quot;:&quot;Add New Role&quot;,&quot;Edit Role&quot;:&quot;Edit Role&quot;,&quot;Role&quot;:&quot;Role&quot;,&quot;Created At&quot;:&quot;Created At&quot;,&quot;Actions&quot;:&quot;Actions&quot;,&quot;Search User&quot;:&quot;Search User&quot;,&quot;Displaying _START_ to _END_ of _TOTAL_ entries&quot;:&quot;Displaying _START_ to _END_ of _TOTAL_ entries&quot;,&quot;Showing _START_ to _END_ of _TOTAL_ entries&quot;:&quot;Showing _START_ to _END_ of _TOTAL_ entries&quot;,&quot;Search...&quot;:&quot;Search...&quot;,&quot;add new role&quot;:&quot;add new role&quot;,&quot;role&quot;:&quot;role&quot;,&quot;role name&quot;:&quot;role name&quot;,&quot;guard&quot;:&quot;guard&quot;,&quot;Administrator&quot;:&quot;Administrator&quot;,&quot;Driver&quot;:&quot;Driver&quot;,&quot;Details of&quot;:&quot;Details of&quot;,&quot;There is no Permissions found!&quot;:&quot;There is no Permissions found!&quot;,&quot;Error!! can not fiche any Permission&quot;:&quot;Error!! can not fiche any Permission&quot;,&quot;permissions&quot;:&quot;permissions&quot;,&quot;Roles&quot;:&quot;Roles&quot;,&quot;Role Name&quot;:&quot;Role Name&quot;,&quot;Guard&quot;:&quot;Guard&quot;,&quot;Permissions&quot;:&quot;Permissions&quot;,&quot;Home&quot;:&quot;Home&quot;,&quot;Profile&quot;:&quot;Profile&quot;,&quot;Messages&quot;:&quot;Messages&quot;,&quot;Close&quot;:&quot;Close&quot;,&quot;Submit&quot;:&quot;Submit&quot;,&quot;Users&quot;:&quot;Users&quot;,&quot;Active Users&quot;:&quot;Active Users&quot;,&quot;Inactive Users&quot;:&quot;Inactive Users&quot;,&quot;Pending Users&quot;:&quot;Pending Users&quot;,&quot;Add New User&quot;:&quot;Add New User&quot;,&quot;User&quot;:&quot;User&quot;,&quot;Phone&quot;:&quot;Phone&quot;,&quot;Status&quot;:&quot;Status&quot;,&quot;Reset Password&quot;:&quot;Reset Password&quot;,&quot;Add new User&quot;:&quot;Add new User&quot;,&quot;Edit User&quot;:&quot;Edit User&quot;,&quot;Main&quot;:&quot;Main&quot;,&quot;Additional&quot;:&quot;Additional&quot;,&quot;Full Name&quot;:&quot;Full Name&quot;,&quot;<EMAIL>&quot;:&quot;<EMAIL>&quot;,&quot;Enter phone number&quot;:&quot;Enter phone number&quot;,&quot;Password&quot;:&quot;Password&quot;,&quot;Confirm Password&quot;:&quot;Confirm Password&quot;,&quot;User Role&quot;:&quot;User Role&quot;,&quot;Teams&quot;:&quot;Teams&quot;,&quot;Customers&quot;:&quot;Customers&quot;,&quot;Active Customers&quot;:&quot;Active Customers&quot;,&quot;Unverified Customers&quot;:&quot;Unverified Customers&quot;,&quot;Blocked Customers&quot;:&quot;Blocked Customers&quot;,&quot;name&quot;:&quot;name&quot;,&quot;email&quot;:&quot;email&quot;,&quot;phone&quot;:&quot;phone&quot;,&quot;tags&quot;:&quot;tags&quot;,&quot;status&quot;:&quot;status&quot;,&quot;created at&quot;:&quot;created at&quot;,&quot;actions&quot;:&quot;actions&quot;,&quot;Add New Customer&quot;:&quot;Add New Customer&quot;,&quot;Customer Role&quot;:&quot;Customer Role&quot;,&quot;Select Role&quot;:&quot;Select Role&quot;,&quot;Company Info&quot;:&quot;Company Info&quot;,&quot;Company Name&quot;:&quot;Company Name&quot;,&quot;enter company name&quot;:&quot;enter company name&quot;,&quot;Company Address&quot;:&quot;Company Address&quot;,&quot;enter company address&quot;:&quot;enter company address&quot;,&quot;Tags&quot;:&quot;Tags&quot;,&quot;Select Template&quot;:&quot;Select Template&quot;,&quot;-- Select Template&quot;:&quot;-- Select Template&quot;,&quot;--- Select Template&quot;:&quot;--- Select Template&quot;,&quot;Logistics&quot;:&quot;Logistics&quot;,&quot;Fleet&quot;:&quot;Fleet&quot;,&quot;Invoice&quot;:&quot;Invoice&quot;,&quot;Preview&quot;:&quot;Preview&quot;,&quot;Add&quot;:&quot;Add&quot;,&quot;Pages&quot;:&quot;Pages&quot;,&quot;User Profile&quot;:&quot;User Profile&quot;,&quot;Projects&quot;:&quot;Projects&quot;,&quot;Account Settings&quot;:&quot;Account Settings&quot;,&quot;Account&quot;:&quot;Account&quot;,&quot;Security&quot;:&quot;Security&quot;,&quot;Billing &amp; Plans&quot;:&quot;Billing &amp; Plans&quot;,&quot;Notifications&quot;:&quot;Notifications&quot;,&quot;Connections&quot;:&quot;Connections&quot;,&quot;FAQ&quot;:&quot;FAQ&quot;,&quot;Front Pages&quot;:&quot;Front Pages&quot;,&quot;Payment&quot;:&quot;Payment&quot;,&quot;Help Center&quot;:&quot;Help Center&quot;,&quot;Landing&quot;:&quot;Landing&quot;,&quot;Categories&quot;:&quot;Categories&quot;,&quot;Article&quot;:&quot;Article&quot;,&quot;Pricing&quot;:&quot;Pricing&quot;,&quot;Error&quot;:&quot;Error&quot;,&quot;Coming Soon&quot;:&quot;Coming Soon&quot;,&quot;Under Maintenance&quot;:&quot;Under Maintenance&quot;,&quot;Not Authorized&quot;:&quot;Not Authorized&quot;,&quot;Authentications&quot;:&quot;Authentications&quot;,&quot;Login&quot;:&quot;Login&quot;,&quot;Register&quot;:&quot;Register&quot;,&quot;Verify Email&quot;:&quot;Verify Email&quot;,&quot;Forgot Password&quot;:&quot;Forgot Password&quot;,&quot;Two Steps&quot;:&quot;Two Steps&quot;,&quot;Basic&quot;:&quot;Basic&quot;,&quot;Cover&quot;:&quot;Cover&quot;,&quot;Multi-steps&quot;:&quot;Multi-steps&quot;,&quot;Modal Examples&quot;:&quot;Modal Examples&quot;,&quot;Wizard Examples&quot;:&quot;Wizard Examples&quot;,&quot;Checkout&quot;:&quot;Checkout&quot;,&quot;Property Listing&quot;:&quot;Property Listing&quot;,&quot;Create Deal&quot;:&quot;Create Deal&quot;,&quot;Icons&quot;:&quot;Icons&quot;,&quot;Tabler&quot;:&quot;Tabler&quot;,&quot;Fontawesome&quot;:&quot;Fontawesome&quot;,&quot;User interface&quot;:&quot;User interface&quot;,&quot;Accordion&quot;:&quot;Accordion&quot;,&quot;Alerts&quot;:&quot;Alerts&quot;,&quot;App Brand&quot;:&quot;App Brand&quot;,&quot;Badges&quot;:&quot;Badges&quot;,&quot;Buttons&quot;:&quot;Buttons&quot;,&quot;Cards&quot;:&quot;Cards&quot;,&quot;Advance&quot;:&quot;Advance&quot;,&quot;Statistics&quot;:&quot;Statistics&quot;,&quot;Analytics&quot;:&quot;Analytics&quot;,&quot;Carousel&quot;:&quot;Carousel&quot;,&quot;Collapse&quot;:&quot;Collapse&quot;,&quot;Dropdowns&quot;:&quot;Dropdowns&quot;,&quot;Footer&quot;:&quot;Footer&quot;,&quot;List Groups&quot;:&quot;List Groups&quot;,&quot;Modals&quot;:&quot;Modals&quot;,&quot;Menu&quot;:&quot;Menu&quot;,&quot;Navbar&quot;:&quot;Navbar&quot;,&quot;Offcanvas&quot;:&quot;Offcanvas&quot;,&quot;Pagination &amp; Breadcrumbs&quot;:&quot;Pagination &amp; Breadcrumbs&quot;,&quot;Progress&quot;:&quot;Progress&quot;,&quot;Spinners&quot;:&quot;Spinners&quot;,&quot;Tabs &amp; Pills&quot;:&quot;Tabs &amp; Pills&quot;,&quot;Toasts&quot;:&quot;Toasts&quot;,&quot;Tooltips &amp; Popovers&quot;:&quot;Tooltips &amp; Popovers&quot;,&quot;Typography&quot;:&quot;Typography&quot;,&quot;Extended UI&quot;:&quot;Extended UI&quot;,&quot;Avatar&quot;:&quot;Avatar&quot;,&quot;BlockUI&quot;:&quot;BlockUI&quot;,&quot;Drag &amp; Drop&quot;:&quot;Drag &amp; Drop&quot;,&quot;Media Player&quot;:&quot;Media Player&quot;,&quot;Perfect Scrollbar&quot;:&quot;Perfect Scrollbar&quot;,&quot;Star Ratings&quot;:&quot;Star Ratings&quot;,&quot;SweetAlert2&quot;:&quot;SweetAlert2&quot;,&quot;Text Divider&quot;:&quot;Text Divider&quot;,&quot;Timeline&quot;:&quot;Timeline&quot;,&quot;Fullscreen&quot;:&quot;Fullscreen&quot;,&quot;Tour&quot;:&quot;Tour&quot;,&quot;Treeview&quot;:&quot;Treeview&quot;,&quot;Miscellaneous&quot;:&quot;Miscellaneous&quot;,&quot;Misc&quot;:&quot;Misc&quot;,&quot;Form Elements&quot;:&quot;Form Elements&quot;,&quot;Basic Inputs&quot;:&quot;Basic Inputs&quot;,&quot;Input groups&quot;:&quot;Input groups&quot;,&quot;Custom Options&quot;:&quot;Custom Options&quot;,&quot;Editors&quot;:&quot;Editors&quot;,&quot;File Upload&quot;:&quot;File Upload&quot;,&quot;Pickers&quot;:&quot;Pickers&quot;,&quot;Select &amp; Tags&quot;:&quot;Select &amp; Tags&quot;,&quot;Sliders&quot;:&quot;Sliders&quot;,&quot;Switches&quot;:&quot;Switches&quot;,&quot;Extras&quot;:&quot;Extras&quot;,&quot;Form Layouts&quot;:&quot;Form Layouts&quot;,&quot;Vertical Form&quot;:&quot;Vertical Form&quot;,&quot;Horizontal Form&quot;:&quot;Horizontal Form&quot;,&quot;Sticky Actions&quot;:&quot;Sticky Actions&quot;,&quot;Form Wizard&quot;:&quot;Form Wizard&quot;,&quot;Numbered&quot;:&quot;Numbered&quot;,&quot;Advanced&quot;:&quot;Advanced&quot;,&quot;Forms&quot;:&quot;Forms&quot;,&quot;Form Validation&quot;:&quot;Form Validation&quot;,&quot;Tables&quot;:&quot;Tables&quot;,&quot;Datatables&quot;:&quot;Datatables&quot;,&quot;Extensions&quot;:&quot;Extensions&quot;,&quot;Charts&quot;:&quot;Charts&quot;,&quot;Apex Charts&quot;:&quot;Apex Charts&quot;,&quot;ChartJS&quot;:&quot;ChartJS&quot;,&quot;Leaflet Maps&quot;:&quot;Leaflet Maps&quot;,&quot;Support&quot;:&quot;Support&quot;,&quot;Documentation&quot;:&quot;Documentation&quot;,&quot;Academy&quot;:&quot;Academy&quot;,&quot;My Course&quot;:&quot;My Course&quot;,&quot;Course Details&quot;:&quot;Course Details&quot;,&quot;Apps &amp; Pages&quot;:&quot;Apps &amp; Pages&quot;,&quot;Components&quot;:&quot;Components&quot;,&quot;Forms &amp; Tables&quot;:&quot;Forms &amp; Tables&quot;,&quot;Charts &amp; Maps&quot;:&quot;Charts &amp; Maps&quot;,&quot;Id&quot;:&quot;Id&quot;,&quot;General&quot;:&quot;General&quot;,&quot;Template&quot;:&quot;Template&quot;,&quot;Donut drag\\u00e9e jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.&quot;:&quot;Donut drag\\u00e9e jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.&quot;,&quot;Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.&quot;:&quot;Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.&quot;,&quot;Geo-fence&quot;:&quot;Geo-fence&quot;,&quot;It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.&quot;:&quot;It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.&quot;,&quot;Add New Geo-fence&quot;:&quot;Add New Geo-fence&quot;,&quot;\\ud83d\\udd0d Search Team&quot;:&quot;\\ud83d\\udd0d Search Team&quot;,&quot;Geofences&quot;:&quot;Geofences&quot;,&quot;Add Geo-fence&quot;:&quot;Add Geo-fence&quot;,&quot;Select Teams&quot;:&quot;Select Teams&quot;,&quot;Name&quot;:&quot;Name&quot;,&quot;Enter name&quot;:&quot;Enter name&quot;,&quot;Description&quot;:&quot;Description&quot;,&quot;Enter description&quot;:&quot;Enter description&quot;,&quot;The role name is required.&quot;:&quot;The role name is required.&quot;,&quot;The role name has already been taken.&quot;:&quot;The role name has already been taken.&quot;,&quot;The guard field is required.&quot;:&quot;The guard field is required.&quot;,&quot;The selected guard is invalid.&quot;:&quot;The selected guard is invalid.&quot;,&quot;At least one permission must be selected.&quot;:&quot;At least one permission must be selected.&quot;,&quot;Permissions must be an array.&quot;:&quot;Permissions must be an array.&quot;,&quot;Error to Save Role&quot;:&quot;Error to Save Role&quot;,&quot;Role Saved&quot;:&quot;Role Saved&quot;,&quot;This role can not be deleted&quot;:&quot;This role can not be deleted&quot;,&quot;There are users connected with this role&quot;:&quot;There are users connected with this role&quot;,&quot;Error to delete role&quot;:&quot;Error to delete role&quot;,&quot;Role deleted&quot;:&quot;Role deleted&quot;,&quot;Rate&quot;:&quot;Rate&quot;,&quot;Fixed&quot;:&quot;Fixed&quot;,&quot;Commission Rate&quot;:&quot;Commission Rate&quot;,&quot;Commission fixed Amount&quot;:&quot;Commission fixed Amount&quot;,&quot;General Settings&quot;:&quot;General Settings&quot;,&quot;You can manage the main and vital settings of the platform from here, so be careful.&quot;:&quot;You can manage the main and vital settings of the platform from here, so be careful.&quot;,&quot;Templates&quot;:&quot;Templates&quot;,&quot;Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more&quot;:&quot;Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more&quot;,&quot;Add New Template&quot;:&quot;Add New Template&quot;,&quot;Template Name&quot;:&quot;Template Name&quot;,&quot;enter the Template name&quot;:&quot;enter the Template name&quot;,&quot;Default Customer Template&quot;:&quot;Default Customer Template&quot;,&quot;Default Driver Template&quot;:&quot;Default Driver Template&quot;,&quot;Default User Template&quot;:&quot;Default User Template&quot;,&quot;Default Task Template&quot;:&quot;Default Task Template&quot;,&quot;Drivers Commission&quot;:&quot;Drivers Commission&quot;,&quot;Commission Type&quot;:&quot;Commission Type&quot;,&quot;The user id is required.&quot;:&quot;The user id is required.&quot;,&quot;The selected user does not exist.&quot;:&quot;The selected user does not exist.&quot;,&quot;The status field is required.&quot;:&quot;The status field is required.&quot;,&quot;Error to Change user Status&quot;:&quot;Error to Change user Status&quot;,&quot;User Status changed&quot;:&quot;User Status changed&quot;,&quot;The name field is required.&quot;:&quot;The name field is required.&quot;,&quot;The email field is required.&quot;:&quot;The email field is required.&quot;,&quot;The email has already been taken.&quot;:&quot;The email has already been taken.&quot;,&quot;The phone field is required.&quot;:&quot;The phone field is required.&quot;,&quot;The phone has already been taken.&quot;:&quot;The phone has already been taken.&quot;,&quot;The password field is required.&quot;:&quot;The password field is required.&quot;,&quot;The password and confirmation must match.&quot;:&quot;The password and confirmation must match.&quot;,&quot;The user role is required.&quot;:&quot;The user role is required.&quot;,&quot;The selected role is invalid.&quot;:&quot;The selected role is invalid.&quot;,&quot;Teams must be an array.&quot;:&quot;Teams must be an array.&quot;,&quot;Customers must be an array.&quot;:&quot;Customers must be an array.&quot;,&quot;The selected template is invalid.&quot;:&quot;The selected template is invalid.&quot;,&quot;The :label field is required.&quot;:&quot;The :label field is required.&quot;,&quot;Can not find the selected user&quot;:&quot;Can not find the selected user&quot;,&quot;User saved successfully&quot;:&quot;User saved successfully&quot;,&quot;User not found&quot;:&quot;User not found&quot;,&quot;Error to change reset password  status&quot;:&quot;Error to change reset password  status&quot;,&quot;User deleted&quot;:&quot;User deleted&quot;,&quot;The selected User has teams to mange. you can not delete hem right now&quot;:&quot;The selected User has teams to mange. you can not delete hem right now&quot;,&quot;Error to delete User&quot;:&quot;Error to delete User&quot;,&quot;Vehicles&quot;:&quot;Vehicles&quot;,&quot;Managing the types of vehicles and trucks that will provide delivery services on the platform&quot;:&quot;Managing the types of vehicles and trucks that will provide delivery services on the platform&quot;,&quot;Vehicles Types&quot;:&quot;Vehicles Types&quot;,&quot;Vehicles Sizes&quot;:&quot;Vehicles Sizes&quot;,&quot;vehicle name&quot;:&quot;vehicle name&quot;,&quot;English name&quot;:&quot;English name&quot;,&quot;save&quot;:&quot;save&quot;,&quot;types&quot;:&quot;types&quot;,&quot;Select vehicle&quot;:&quot;Select vehicle&quot;,&quot;select vehicle&quot;:&quot;select vehicle&quot;,&quot;Flitter by vehicle&quot;:&quot;Flitter by vehicle&quot;,&quot;all vehicle&quot;:&quot;all vehicle&quot;,&quot;vehicle&quot;:&quot;vehicle&quot;,&quot;type name&quot;:&quot;type name&quot;,&quot;sizes&quot;:&quot;sizes&quot;,&quot;size&quot;:&quot;size&quot;,&quot;Select vehicle Type&quot;:&quot;Select vehicle Type&quot;,&quot;Flitter by vehicle type&quot;:&quot;Flitter by vehicle type&quot;,&quot;select vehicle type&quot;:&quot;select vehicle type&quot;,&quot;vehicle type&quot;:&quot;vehicle type&quot;,&quot;No data available&quot;:&quot;No data available&quot;,&quot;select vehicle Size&quot;:&quot;select vehicle Size&quot;,&quot;Add New Tag&quot;:&quot;Add New Tag&quot;,&quot;tag name&quot;:&quot;tag name&quot;,&quot;enter the tag name&quot;:&quot;enter the tag name&quot;,&quot;tag slug&quot;:&quot;tag slug&quot;,&quot;enter the tag slug&quot;:&quot;enter the tag slug&quot;,&quot;Select Tags&quot;:&quot;Select Tags&quot;,&quot;The tag name is required.&quot;:&quot;The tag name is required.&quot;,&quot;The tag name has already been taken.&quot;:&quot;The tag name has already been taken.&quot;,&quot;The tag slug is required.&quot;:&quot;The tag slug is required.&quot;,&quot;The tag slug has already been taken.&quot;:&quot;The tag slug has already been taken.&quot;,&quot;The description must be a string.&quot;:&quot;The description must be a string.&quot;,&quot;The description may not be greater than 400 characters.&quot;:&quot;The description may not be greater than 400 characters.&quot;,&quot;Can not find the selected Tag&quot;:&quot;Can not find the selected Tag&quot;,&quot;Error: can not save the Tag&quot;:&quot;Error: can not save the Tag&quot;,&quot;Tag saved successfully&quot;:&quot;Tag saved successfully&quot;,&quot;Error to find selected Tag&quot;:&quot;Error to find selected Tag&quot;,&quot;Error to delete Tag&quot;:&quot;Error to delete Tag&quot;,&quot;Tag deleted&quot;:&quot;Tag deleted&quot;,&quot;The geofence name is required.&quot;:&quot;The geofence name is required.&quot;,&quot;The geofence name has already been taken.&quot;:&quot;The geofence name has already been taken.&quot;,&quot;The coordinates field is required.&quot;:&quot;The coordinates field is required.&quot;,&quot;The coordinates must be a string.&quot;:&quot;The coordinates must be a string.&quot;,&quot;Can not find the selected Geo-Fence&quot;:&quot;Can not find the selected Geo-Fence&quot;,&quot;error to save Geo-Fence&quot;:&quot;error to save Geo-Fence&quot;,&quot;Geo-Fence saved successfully&quot;:&quot;Geo-Fence saved successfully&quot;,&quot;Error to delete Geo-fence&quot;:&quot;Error to delete Geo-fence&quot;,&quot;Geo-fence deleted&quot;:&quot;Geo-fence deleted&quot;,&quot;Points&quot;:&quot;Points&quot;,&quot;Add New Point&quot;:&quot;Add New Point&quot;,&quot;address&quot;:&quot;address&quot;,&quot;customer&quot;:&quot;customer&quot;,&quot;enter the point name&quot;:&quot;enter the point name&quot;,&quot;enter the point address&quot;:&quot;enter the point address&quot;,&quot;Location&quot;:&quot;Location&quot;,&quot;confirm location&quot;:&quot;confirm location&quot;,&quot;Contact name&quot;:&quot;Contact name&quot;,&quot;enter the point contact name&quot;:&quot;enter the point contact name&quot;,&quot;Contact phone&quot;:&quot;Contact phone&quot;,&quot;enter the point contact phone&quot;:&quot;enter the point contact phone&quot;,&quot;Select Customer&quot;:&quot;Select Customer&quot;,&quot;The point name is required.&quot;:&quot;The point name is required.&quot;,&quot;The point name must be a string.&quot;:&quot;The point name must be a string.&quot;,&quot;The contact name must be a string.&quot;:&quot;The contact name must be a string.&quot;,&quot;The contact name may not be greater than 400 characters.&quot;:&quot;The contact name may not be greater than 400 characters.&quot;,&quot;The contact phone must be a string.&quot;:&quot;The contact phone must be a string.&quot;,&quot;The contact phone may not be greater than 50 characters.&quot;:&quot;The contact phone may not be greater than 50 characters.&quot;,&quot;The address field is required.&quot;:&quot;The address field is required.&quot;,&quot;The address must be a string.&quot;:&quot;The address must be a string.&quot;,&quot;The address may not be greater than 500 characters.&quot;:&quot;The address may not be greater than 500 characters.&quot;,&quot;The latitude field is required.&quot;:&quot;The latitude field is required.&quot;,&quot;The latitude must be a number.&quot;:&quot;The latitude must be a number.&quot;,&quot;The longitude field is required.&quot;:&quot;The longitude field is required.&quot;,&quot;The longitude must be a number.&quot;:&quot;The longitude must be a number.&quot;,&quot;The selected customer is invalid.&quot;:&quot;The selected customer is invalid.&quot;,&quot;Can not find the selected Point&quot;:&quot;Can not find the selected Point&quot;,&quot;Error: can not save the Point&quot;:&quot;Error: can not save the Point&quot;,&quot;Point saved successfully&quot;:&quot;Point saved successfully&quot;,&quot;Error to delete Point. its connect with pricing mater&quot;:&quot;Error to delete Point. its connect with pricing mater&quot;,&quot;Error to delete Point&quot;:&quot;Error to delete Point&quot;,&quot;Point deleted&quot;:&quot;Point deleted&quot;,&quot;Blockages&quot;:&quot;Blockages&quot;,&quot;Add a new Blockage&quot;:&quot;Add a new Blockage&quot;,&quot;type&quot;:&quot;type&quot;,&quot;description&quot;:&quot;description&quot;,&quot;coordinates&quot;:&quot;coordinates&quot;,&quot;Block Type&quot;:&quot;Block Type&quot;,&quot;Select Type&quot;:&quot;Select Type&quot;,&quot;Point Closed&quot;:&quot;Point Closed&quot;,&quot;Line Closed&quot;:&quot;Line Closed&quot;,&quot;Block Description&quot;:&quot;Block Description&quot;,&quot;optional&quot;:&quot;optional&quot;,&quot;draw the Points on the map&quot;:&quot;draw the Points on the map&quot;,&quot;Save&quot;:&quot;Save&quot;,&quot;Cancel&quot;:&quot;Cancel&quot;,&quot;The blockage type is required.&quot;:&quot;The blockage type is required.&quot;,&quot;The selected blockage type is invalid.&quot;:&quot;The selected blockage type is invalid.&quot;,&quot;Error: can not save the Blockage&quot;:&quot;Error: can not save the Blockage&quot;,&quot;Blockage saved successfully&quot;:&quot;Blockage saved successfully&quot;,&quot;Error to delete Blockage&quot;:&quot;Error to delete Blockage&quot;,&quot;Blockage deleted&quot;:&quot;Blockage deleted&quot;,&quot;Pricing Methods&quot;:&quot;Pricing Methods&quot;,&quot;Method&quot;:&quot;Method&quot;,&quot;Add New Method&quot;:&quot;Add New Method&quot;,&quot;Method Name&quot;:&quot;Method Name&quot;,&quot;enter the Method name&quot;:&quot;enter the Method name&quot;,&quot;Edit Method&quot;:&quot;Edit Method&quot;,&quot;The method name is required.&quot;:&quot;The method name is required.&quot;,&quot;The method name has already been taken.&quot;:&quot;The method name has already been taken.&quot;,&quot;The description field is required.&quot;:&quot;The description field is required.&quot;,&quot;Can not find the selected Pricing Method&quot;:&quot;Can not find the selected Pricing Method&quot;,&quot;error to save Pricing Method&quot;:&quot;error to save Pricing Method&quot;,&quot;Pricing Method saved successfully&quot;:&quot;Pricing Method saved successfully&quot;,&quot;Pricing Method not found&quot;:&quot;Pricing Method not found&quot;,&quot;Error to change Pricing Method status&quot;:&quot;Error to change Pricing Method status&quot;,&quot;label&quot;:&quot;label&quot;,&quot;driver can&quot;:&quot;driver can&quot;,&quot;customer can&quot;:&quot;customer can&quot;,&quot;value&quot;:&quot;value&quot;,&quot;require&quot;:&quot;require&quot;,&quot;Select Values&quot;:&quot;Select Values&quot;,&quot;Value&quot;:&quot;Value&quot;,&quot;Display Name&quot;:&quot;Display Name&quot;,&quot;Action&quot;:&quot;Action&quot;,&quot;More&quot;:&quot;More&quot;,&quot;Customers Selections&quot;:&quot;Customers Selections&quot;,&quot;Apply to All Customers&quot;:&quot;Apply to All Customers&quot;,&quot;Use customers tags&quot;:&quot;Use customers tags&quot;,&quot;Use Specific customers&quot;:&quot;Use Specific customers&quot;,&quot;Vehicles Selections&quot;:&quot;Vehicles Selections&quot;,&quot;Base Fare&quot;:&quot;Base Fare&quot;,&quot;Base Distance&quot;:&quot;Base Distance&quot;,&quot;Base Waiting&quot;:&quot;Base Waiting&quot;,&quot;Distance Fare&quot;:&quot;Distance Fare&quot;,&quot;Waiting Fare&quot;:&quot;Waiting Fare&quot;,&quot;Dynamic Pricing Based on Field Values&quot;:&quot;Dynamic Pricing Based on Field Values&quot;,&quot;add field&quot;:&quot;add field&quot;,&quot;Dynamic Pricing Based on Geo-fence&quot;:&quot;Dynamic Pricing Based on Geo-fence&quot;,&quot;add geofence&quot;:&quot;add geofence&quot;,&quot;Commission&quot;:&quot;Commission&quot;,&quot;VAT Commission&quot;:&quot;VAT Commission&quot;,&quot;Service Tax Commission&quot;:&quot;Service Tax Commission&quot;,&quot;Discount Fare&quot;:&quot;Discount Fare&quot;,&quot;Discount percentage %&quot;:&quot;Discount percentage %&quot;,&quot;The rule name is required.&quot;:&quot;The rule name is required.&quot;,&quot;The rule name must be a string.&quot;:&quot;The rule name must be a string.&quot;,&quot;The rule name may not be greater than 255 characters.&quot;:&quot;The rule name may not be greater than 255 characters.&quot;,&quot;The rule name has already been taken.&quot;:&quot;The rule name has already been taken.&quot;,&quot;The decimal places field is required.&quot;:&quot;The decimal places field is required.&quot;,&quot;The decimal places must be an integer.&quot;:&quot;The decimal places must be an integer.&quot;,&quot;The decimal places must be at least 0.&quot;:&quot;The decimal places must be at least 0.&quot;,&quot;The decimal places may not be greater than 10.&quot;:&quot;The decimal places may not be greater than 10.&quot;,&quot;The form template is required.&quot;:&quot;The form template is required.&quot;,&quot;The form template id must be an integer.&quot;:&quot;The form template id must be an integer.&quot;,&quot;The selected form template is invalid.&quot;:&quot;The selected form template is invalid.&quot;,&quot;At least one customer must be selected.&quot;:&quot;At least one customer must be selected.&quot;,&quot;Each customer is required.&quot;:&quot;Each customer is required.&quot;,&quot;Each customer id must be an integer.&quot;:&quot;Each customer id must be an integer.&quot;,&quot;At least one tag must be selected.&quot;:&quot;At least one tag must be selected.&quot;,&quot;Tags must be an array.&quot;:&quot;Tags must be an array.&quot;,&quot;Each tag is required.&quot;:&quot;Each tag is required.&quot;,&quot;Each tag id must be an integer.&quot;:&quot;Each tag id must be an integer.&quot;,&quot;The selected tag is invalid.&quot;:&quot;The selected tag is invalid.&quot;,&quot;At least one vehicle size must be selected.&quot;:&quot;At least one vehicle size must be selected.&quot;,&quot;Sizes must be an array.&quot;:&quot;Sizes must be an array.&quot;,&quot;Each size id must be an integer.&quot;:&quot;Each size id must be an integer.&quot;,&quot;The selected vehicle size is invalid.&quot;:&quot;The selected vehicle size is invalid.&quot;,&quot;The base fare field is required.&quot;:&quot;The base fare field is required.&quot;,&quot;The base fare must be a number.&quot;:&quot;The base fare must be a number.&quot;,&quot;The base fare must be at least 0.&quot;:&quot;The base fare must be at least 0.&quot;,&quot;The base distance field is required.&quot;:&quot;The base distance field is required.&quot;,&quot;The base distance must be a number.&quot;:&quot;The base distance must be a number.&quot;,&quot;The base distance must be at least 0.&quot;:&quot;The base distance must be at least 0.&quot;,&quot;The base waiting field is required.&quot;:&quot;The base waiting field is required.&quot;,&quot;The base waiting must be a number.&quot;:&quot;The base waiting must be a number.&quot;,&quot;The base waiting must be at least 0.&quot;:&quot;The base waiting must be at least 0.&quot;,&quot;The distance fare field is required.&quot;:&quot;The distance fare field is required.&quot;,&quot;The distance fare must be a number.&quot;:&quot;The distance fare must be a number.&quot;,&quot;The distance fare must be at least 0.&quot;:&quot;The distance fare must be at least 0.&quot;,&quot;The waiting fare field is required.&quot;:&quot;The waiting fare field is required.&quot;,&quot;The waiting fare must be a number.&quot;:&quot;The waiting fare must be a number.&quot;,&quot;The waiting fare must be at least 0.&quot;:&quot;The waiting fare must be at least 0.&quot;,&quot;The VAT commission field is required.&quot;:&quot;The VAT commission field is required.&quot;,&quot;The VAT commission must be a number.&quot;:&quot;The VAT commission must be a number.&quot;,&quot;The VAT commission must be at least 0.&quot;:&quot;The VAT commission must be at least 0.&quot;,&quot;The VAT commission may not be greater than 100.&quot;:&quot;The VAT commission may not be greater than 100.&quot;,&quot;The service commission field is required.&quot;:&quot;The service commission field is required.&quot;,&quot;The service commission must be a number.&quot;:&quot;The service commission must be a number.&quot;,&quot;The service commission must be at least 0.&quot;:&quot;The service commission must be at least 0.&quot;,&quot;The service commission may not be greater than 100.&quot;:&quot;The service commission may not be greater than 100.&quot;,&quot;The discount must be a number.&quot;:&quot;The discount must be a number.&quot;,&quot;The discount must be at least 0.&quot;:&quot;The discount must be at least 0.&quot;,&quot;The discount may not be greater than 100.&quot;:&quot;The discount may not be greater than 100.&quot;,&quot;Task Done&quot;:&quot;Task Done&quot;,&quot;Running Tasks&quot;:&quot;Running Tasks&quot;,&quot;Details&quot;:&quot;Details&quot;,&quot;Edit&quot;:&quot;Edit&quot;,&quot;Suspend&quot;:&quot;Suspend&quot;,&quot;Tasks&quot;:&quot;Tasks&quot;,&quot;Total&quot;:&quot;Total&quot;,&quot;Issued Date&quot;:&quot;Issued Date&quot;,&quot;Additional Fields&quot;:&quot;Additional Fields&quot;,&quot;No additional data found for this customer.&quot;:&quot;No additional data found for this customer.&quot;,&quot;View&quot;:&quot;View&quot;,&quot;Change Status&quot;:&quot;Change Status&quot;,&quot;Create Wallet&quot;:&quot;Create Wallet&quot;,&quot;The customer id is required.&quot;:&quot;The customer id is required.&quot;,&quot;The selected customer does not exist.&quot;:&quot;The selected customer does not exist.&quot;,&quot;You do not have permission to do actions to this record&quot;:&quot;You do not have permission to do actions to this record&quot;,&quot;Error to Change Customer Status&quot;:&quot;Error to Change Customer Status&quot;,&quot;Customer Status changed&quot;:&quot;Customer Status changed&quot;,&quot;Can not find the selected Customer&quot;:&quot;Can not find the selected Customer&quot;,&quot;Error: can not save the Customer&quot;:&quot;Error: can not save the Customer&quot;,&quot;Customer saved successfully&quot;:&quot;Customer saved successfully&quot;,&quot;Error to delete Customer&quot;:&quot;Error to delete Customer&quot;,&quot;Customer deleted&quot;:&quot;Customer deleted&quot;,&quot;The driver id is required.&quot;:&quot;The driver id is required.&quot;,&quot;The selected driver does not exist.&quot;:&quot;The selected driver does not exist.&quot;,&quot;Error to Change Driver Status&quot;:&quot;Error to Change Driver Status&quot;,&quot;Driver Status changed&quot;:&quot;Driver Status changed&quot;,&quot;Can not find the selected Driver&quot;:&quot;Can not find the selected Driver&quot;,&quot;Error: can not save the Driver&quot;:&quot;Error: can not save the Driver&quot;,&quot;Driver saved successfully&quot;:&quot;Driver saved successfully&quot;,&quot;Error to delete Driver&quot;:&quot;Error to delete Driver&quot;,&quot;Driver deleted&quot;:&quot;Driver deleted&quot;,&quot;Drivers&quot;:&quot;Drivers&quot;,&quot;Active Drivers&quot;:&quot;Active Drivers&quot;,&quot;Pending Drivers&quot;:&quot;Pending Drivers&quot;,&quot;Blocked Drivers&quot;:&quot;Blocked Drivers&quot;,&quot;Unverified Drivers&quot;:&quot;Unverified Drivers&quot;,&quot;username&quot;:&quot;username&quot;,&quot;Add new Driver&quot;:&quot;Add new Driver&quot;,&quot;Username&quot;:&quot;Username&quot;,&quot;Team&quot;:&quot;Team&quot;,&quot;Select Team&quot;:&quot;Select Team&quot;,&quot;Driver Role&quot;:&quot;Driver Role&quot;,&quot;Home Address&quot;:&quot;Home Address&quot;,&quot;enter home address&quot;:&quot;enter home address&quot;,&quot;Select Commission Type&quot;:&quot;Select Commission Type&quot;,&quot;\\u064cRate&quot;:&quot;Rate&quot;,&quot;Fixed Amount&quot;:&quot;Fixed Amount&quot;,&quot;Subscription Monthly&quot;:&quot;Subscription Monthly&quot;,&quot;Commission Amount&quot;:&quot;Commission Amount&quot;,&quot;Vehicle Selection&quot;:&quot;Vehicle Selection&quot;,&quot;Wallets&quot;:&quot;Wallets&quot;,&quot;balance&quot;:&quot;balance&quot;,&quot;Debt Ceiling&quot;:&quot;Debt Ceiling&quot;,&quot;preview&quot;:&quot;preview&quot;,&quot;last transaction&quot;:&quot;last transaction&quot;,&quot;Amount&quot;:&quot;Amount&quot;,&quot;Maturity&quot;:&quot;Maturity&quot;,&quot;Task&quot;:&quot;Task&quot;,&quot;Add New Transaction&quot;:&quot;Add New Transaction&quot;,&quot;Enter the amount&quot;:&quot;Enter the amount&quot;,&quot;Transaction Type&quot;:&quot;Transaction Type&quot;,&quot;Credit&quot;:&quot;Credit&quot;,&quot;Debit&quot;:&quot;Debit&quot;,&quot;Maturity Time&quot;:&quot;Maturity Time&quot;,&quot;Optional notes...&quot;:&quot;Optional notes...&quot;,&quot;Image &quot;:&quot;Image &quot;,&quot;View the image&quot;:&quot;View the image&quot;,&quot;close&quot;:&quot;close&quot;,&quot;image&quot;:&quot;image&quot;}</span>\"
</pre><script>Sfdump(\"sf-dump-1073181822\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Unable to locate file in Vite manifest: resources/js/notes.js. at C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:987)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(390): Illuminate\\Foundation\\Vite->chunk(Array, 'resources/js/no...')
#1 C:\\xampp\\htdocs\\safedestssss\\resources\\views\\layouts\\sections\\scripts.blade.php(9): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#5 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#7 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#9 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\xampp\\htdocs\\safedestssss\\resources\\views\\layouts\\commonMaster.blade.php(82): Illuminate\\View\\View->render()
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#17 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#19 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#20 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#21 C:\\xampp\\htdocs\\safedestssss\\resources\\views\\layouts\\blankLayout.blade.php(20): Illuminate\\View\\View->render()
#22 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#23 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#24 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#25 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#26 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#27 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#28 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#29 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#30 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#31 C:\\xampp\\htdocs\\safedestssss\\resources\\views\\auth\\custom-login.blade.php(720): Illuminate\\View\\View->render()
#32 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#33 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#34 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#35 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#36 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#37 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#38 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#39 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#40 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#41 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#42 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#43 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#44 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#45 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#46 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'web')
#49 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\LocaleMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#59 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#68 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#91 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#92 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#93 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#94 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteException(code: 0): Unable to locate file in Vite manifest: resources/js/notes.js. at C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:987)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(390): Illuminate\\Foundation\\Vite->chunk(Array, 'resources/js/no...')
#1 C:\\xampp\\htdocs\\safedestssss\\storage\\framework\\views\\3d771d510237e559c1d72e6918bae9a7.php(9): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#5 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#7 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#9 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 C:\\xampp\\htdocs\\safedestssss\\storage\\framework\\views\\356ae9133a65f96f953ba2d4795f5e5f.php(83): Illuminate\\View\\View->render()
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#17 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#19 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#20 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#21 C:\\xampp\\htdocs\\safedestssss\\storage\\framework\\views\\43640f76538ceebc6702860ad4956d9f.php(23): Illuminate\\View\\View->render()
#22 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#23 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#24 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#25 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#26 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#27 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#28 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#29 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#30 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#31 C:\\xampp\\htdocs\\safedestssss\\storage\\framework\\views\\6f85506e8d7f02e808dd35c8d084bcec.php(760): Illuminate\\View\\View->render()
#32 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#33 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#34 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#35 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#36 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#37 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#38 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#39 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#40 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#41 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#42 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#43 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#44 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#45 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#46 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'web')
#49 C:\\xampp\\htdocs\\safedestssss\\app\\Http\\Middleware\\LocaleMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#59 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#67 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#68 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#91 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#92 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#93 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#94 {main}
"} 
[2025-08-28 06:23:19] local.INFO: Validating reCAPTCHA - First attempt...  
[2025-08-28 06:23:19] local.INFO: Captcha validated successfully  
[2025-08-28 06:23:22] local.INFO: Captcha already validated in this request, skipping validation  
[2025-08-28 12:03:22] local.INFO: Validating reCAPTCHA - First attempt...  
[2025-08-28 12:03:22] local.INFO: Captcha validated successfully  
[2025-08-28 12:03:23] local.INFO: Captcha already validated in this request, skipping validation  
[2025-08-28 12:03:23] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1133): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1043): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(994): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(198): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\safedestssss\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#47 {main}
"} 
[2025-08-28 12:03:26] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1133): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1043): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(994): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(258): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(216): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\xampp\\htdocs\\safedestssss\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\xampp\\htdocs\\safedestssss\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 {main}
"} 
